import React from 'react';
import ReactDOM from 'react-dom/client';

import reportWebVitals from './reportWebVitals';

import App from './App';
import AppProvider from './store';

import { BrowserRouter as Router } from 'react-router-dom';

import ErrorBoundaryProvider from './components/UtilComponents/error-boundary/';

import * as Sentry from '@sentry/react';
// import { ErrorBoundary } from '@sentry/react';

import { QueryClientProvider, QueryClient } from 'react-query';

import { ReactQueryDevtools } from 'react-query/devtools';

// Sentry.init({
// 	dsn: 'https://<EMAIL>/4508043445272576',
// 	integrations: [
// 		Sentry.browserTracingIntegration(),
// 		Sentry.browserProfilingIntegration(),
// 		Sentry.replayIntegration(),
// 		Sentry.reactRouterV6BrowserTracingIntegration({
// 			useEffect,
// 			useLocation,
// 			useNavigationType,
// 			createRoutesFromChildren,
// 			matchRoutes,
// 		}),
// 	],
// 	tracesSampleRate: 1.0,
// 	// Set 'tracePropagationTargets' to control for which URLs distributed tracing should be enabled
// 	// tracePropagationTargets: ['localhost', /^https:\/\/yourserver\.io\/api/],
// 	// Session Replay
// 	replaysSessionSampleRate: 0.1,
// 	replaysOnErrorSampleRate: 1.0,
// });

const queryClient = new QueryClient({
	defaultOptions: {
		queries: {
			refetchOnWindowFocus: false,
		},
	},
});

const root = ReactDOM.createRoot(document.getElementById('root') as HTMLElement, {
	// Callback called when React automatically recovers from errors.
	onRecoverableError: Sentry.reactErrorHandler(),
});

root.render(
	<React.StrictMode>
		<ErrorBoundaryProvider>
			<QueryClientProvider client={queryClient}>
				<AppProvider>
					<Router>
						<App />
					</Router>
				</AppProvider>
				<ReactQueryDevtools initialIsOpen={false} />
			</QueryClientProvider>
		</ErrorBoundaryProvider>
	</React.StrictMode>
);

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
