import axios, { AxiosResponse } from 'axios';
import { getEnvs } from './../utils/getEnvs';
import { remoteDB } from './pouch/DBConn';

const { uri, backendUrl } = getEnvs();

const axiosInstance = axios.create({
	baseURL: uri,
	headers: {
		// Authorization: `Basic ${window.btoa("")}`,
	},
});

export const axiosInstanceShoppaBackend = axios.create({
	baseURL: backendUrl,
	withCredentials: true,
});

axiosInstanceShoppaBackend.interceptors.response.use(
	(res: AxiosResponse<any, any>) => {
		return Promise.resolve(res?.data);
	},
	async (error) => {
		console.log(error, error?.response?.config?.url, error?.response?.status);
		if (!error.response) {
			return Promise.reject(error);
		}

		if (!error?.response?.config?.url?.includes("/login") && error.response) {
			if (
				error.response.status === 401 ||
				error.response.status === 403
			) {

				localStorage?.clear();
				await remoteDB('').logOut();
				window.location.href = '/login'
				return Promise.reject(error);
			}
			return Promise.reject(error.response?.data);
		}


		return Promise.reject(error.response);
	}
);

export default axiosInstance;
