import { CartProps } from '../../models';
import { UpdateCartProps } from '../react_query/mutations';

export const binarySearch = (arr: Array<any>, arrId: string, docId: string | number) => {
	let low = 0,
		high = arr.length,
		mid;

	while (low < high) {
		mid = (low + high) >>> 1;
		arr[mid][arrId] < docId ? (low = mid + 1) : (high = mid);
	}
	return low;
};

export const handleDeleted = (docs: Array<any>, id: string | number) => {
	let newDocs = [...docs];
	const index = binarySearch(newDocs, '_id', id);
	var doc = newDocs[index];
	if (doc && doc._id === id) {
		newDocs.splice(index, 1);
	}

	return newDocs;
};

export const handleUpdatedOrInserted = (docs: Array<any>, newDoc: any) => {
	let newDocs = [...docs];
	const index = binarySearch(newDocs, '_id', newDoc._id);
	const doc = newDocs[index];
	if (doc && doc._id === newDoc._id) {
		newDocs[index] = newDoc;
		return newDocs;
	} else {
		newDocs.splice(index, 0, newDoc);
		return newDocs;
	}
};

export const handleUpdateCart = (
	cartItems: CartProps['products'],
	values: UpdateCartProps,
	event?: 'change' | 'click'
) => {
	const productIndex = (cartItems || []).findIndex((cart) => cart.productId === values.productId);

	const existingProduct = cartItems?.[productIndex];

	if (!existingProduct) return null;

	if (existingProduct.productId !== values.productId) return null;

	let newProduct;

	if (event === 'change') {
		newProduct = {
			...existingProduct,
			quantity: +values.quantity || 0,
		};
	} else {
		newProduct = {
			...existingProduct,
			quantity: existingProduct.quantity + 1,
		};
	}

	const items = [...cartItems];
	items.splice(productIndex, 1, newProduct);

	const repositionItems = [
		...items.filter((item) => item.productId === values.productId),
		...items.filter((item) => item.productId !== values.productId),
	];

	return repositionItems;
};

export const handleDeleteCartItem = (cartItems: Array<any>, productId: string) => {
	const items = [...cartItems];

	return items.filter((item: any) => item.productId !== productId);
};
