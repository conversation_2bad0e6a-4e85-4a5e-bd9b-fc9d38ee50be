import { handleShowPinCodeScreen } from '../../../store/slices/authSlice';
import store from '../../../store/storeConfig';
import { getEnvs } from '../../../utils/getEnvs';
import { createPinCodeStatus } from '../../../utils/pincode';
import { saveSession } from '../../../utils/storage';
import { CompanyInfoResponseProps, UserResponseProps } from '../../../types';
import { remoteDB } from '../DBConn';
import { axiosInstanceShoppaBackend } from '../../axios.config';
import { PLATFORMS } from '../../../data/platfroms';

export const shoppaLogin = async (name: string, password: string, navigate: (arg: string) => void) => {
	const { user, companySettings } = getEnvs();

	try {
		const response = await axiosInstanceShoppaBackend.post('/user/login', {
			username: name,
			password,
			platform: PLATFORMS.shoppa_pos,
		});

		const userDetails = response?.data as UserResponseProps;

		const companyInfo = await remoteDB(userDetails?.merchantName).get<CompanyInfoResponseProps>(`merchantInfo`);

		saveSession(user, userDetails);
		saveSession(companySettings, companyInfo);

		const isPincodeEnabled = userDetails?.flags?.pinCodeEnabled;

		const isDisabled = userDetails?.flags?.accountLocked;

		if (isDisabled) {
			throw new Error('Account is currently disabled');
		}

		if (!isPincodeEnabled) {
			navigate('/passcode');
			window.location.reload();
			return;
		}

		if (isPincodeEnabled) {
			await createPinCodeStatus(false);

			navigate('/terminal');

			window.location.reload();
			setTimeout(() => {
				store.dispatch(handleShowPinCodeScreen(true));
			}, 2000);

			return;
		}

		window.location.reload();

		throw new Error('Something went wrong');
	} catch (err: any) {
		throw new Error(err?.data?.message ?? err?.message ?? 'An error occured');
	}
};
