import { genericMerchantDB } from "./../DBConn";

import { optsChangesProducts } from "./../opts";


interface IChanges {
  localDBName: string;
  onError: (arg: any) => void;
  onComplete?: (arg: any) => void;
  onChange: (arg: any) => void;
}

export const shoppaChanges = ({
  localDBName,
  onError,
  onChange,
}: IChanges) => {

  try {
    let changesHandler = genericMerchantDB(localDBName)
      .changes(optsChangesProducts())
      .on("change", onChange)
      .on("error", onError);

    return changesHandler;
  } catch (e) {
    console.log(e);
  }
};
