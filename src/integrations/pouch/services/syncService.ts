import { remoteDB, salesDB } from "./../DBConn";

import { getSession } from "../../../utils/storage";

import { getEnvs } from "./../../../utils/getEnvs";

import { IFilter } from "../../../models";

const { user2 } = getEnvs();

interface IReplicate {
    localDBName: string;
    onError: (arg: any) => void;
    onComplete: (arg: any) => void;
    filter: IFilter;
    filterFunc: (arg: any) => any;
    onChange: any;
}


export const shoppaSync = ({
    localDBName,
    onError,
    onComplete,
    filter,
    filterFunc,
    onChange,
}: IReplicate) => {
    const userInfo: any = getSession(user2);

    try {
        let replicationHandler = salesDB().sync(remoteDB(userInfo?.merchantName), filterFunc(filter))
            .on("complete", onComplete)
            .on("error", onError)
            .on("change", onChange);

        return replicationHandler;
    } catch (e) {
        console.log(e);
    }
};
