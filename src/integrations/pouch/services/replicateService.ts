import { remoteDB, genericMerchantDB } from "./../DBConn";

//import { checkLocalInfo } from "./../checkLocalInfo";

import { getSession } from "../../../utils/storage";

import { getEnvs } from "./../../../utils/getEnvs";

import { IFilter } from "../../../models";

const { user2 } = getEnvs();

interface IReplicate {
  localDBName: string;
  onError: (arg: any) => void;
  onComplete: (arg: any) => void;
  onActive: () => void;
  filter: IFilter;
  filterFunc: (arg: any) => any;
  onChange: any;
}

export const shoppaReplicate = ({
  localDBName,
  onError,
  onComplete,
  onActive,
  filter,
  filterFunc,
  onChange,
}: IReplicate) => {
  const userInfo: any = getSession(user2);

  try {
    let replicationHandler = genericMerchantDB(localDBName)
      .replicate.from(remoteDB(userInfo?.merchantName), filterFunc(filter))
      .on("complete", onComplete)
      .on("error", onError)
      .on("change", onChange).on('denied', () => {
      }).on('paused', () => {
      }).on('active', () => {
        onActive()
      });

    return replicationHandler;
  } catch (e) {
    console.log(e);
  }
};
