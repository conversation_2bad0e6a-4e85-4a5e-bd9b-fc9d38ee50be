import { remoteDB } from '../DBConn';
import { saveSession } from '../../../utils/storage';
import { getSessionInfo } from '../../../utils/getSessionInfo';
import bcryptjs from 'bcryptjs';
import store from '../../../store/storeConfig';
import { handleAuthentication, handleShowPinCodeScreen } from '../../../store/slices/authSlice';
import { NavigateFunction } from 'react-router-dom';
import { updatePinCodeStatus } from '../../../utils/pincode';
import { UserResponseProps } from '../../../types';

const saltRounds = 10;

export const shoppaPinCode = async (passcode: string, navigate: NavigateFunction) => {
	const { userInfo, user } = getSessionInfo();
	try {
		const userData = await remoteDB(userInfo?.merchantName as string).get<UserResponseProps>(userInfo?._id as string);

		const hashPasscode = await bcryptjs.hash(passcode, saltRounds);

		userData.passcode = hashPasscode;
		userData.flags.pinCodeEnabled = true;

		await remoteDB(userInfo?.merchantName as string).put(userData);

		navigate('/terminal');

		saveSession(user, userData);

		store.dispatch(handleAuthentication(true));
	} catch (err: any) {
		throw Error(err?.reason ?? 'Could not set pincode. Try Again');
	}
};

export const verifyPasscode = async (passcode: string) => {
	const { userInfo } = getSessionInfo();

	try {
		const isCorrectPasscode = await bcryptjs.compare(passcode ?? '  ', userInfo?.passcode ?? '');

		if (!isCorrectPasscode) {
			throw new Error('Incorrect passcode');
		} else {
			await updatePinCodeStatus(true);
			store.dispatch(handleShowPinCodeScreen(false));
		}
	} catch (err: any) {
		throw Error(err.message || 'Something went wrong');
	}
};

export const shoppaGetPincode = async (data: any, navigate: any, cb: any) => {
	try {
		await remoteDB(data?.merchantName).get('_local/' + data?.userName);
		cb();
		navigate('/terminal', { replace: true });
	} catch (err) {
		cb();
		navigate('/reset-code', { replace: true });
	}
};
