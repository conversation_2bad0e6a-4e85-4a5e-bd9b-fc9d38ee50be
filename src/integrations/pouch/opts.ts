import { EDDocFilterKeys, EDDocPath } from '../../models';
import { getSessionInfo } from '../../utils/getSessionInfo';
import { IFilter } from './../../models/index';

export const productFilterInitial = (): IFilter => {
	const { locationId } = getSessionInfo();
	return {
		filter: EDDocPath.product_filter_initial,
		query_params: {
			type: EDDocFilterKeys.product,
			locationId: locationId || '',
		},
	};
};

export const productFilterSubsequent = (): IFilter => {
	const { locationId } = getSessionInfo();

	return {
		filter: EDDocPath.product_filter_subsequent,
		query_params: {
			type: EDDocFilterKeys.product,
			locationId: locationId || '',
		},
	};
};

export const cashiersFilter = (): IFilter => {
	const { locationId } = getSessionInfo();

	return {
		filter: EDDocPath.cashier_filter,
		query_params: {
			type: EDDocFilterKeys.cashier,
			locationId: locationId || '',
		},
	};
};

export const paymentTypesFilter = (): IFilter => {
	const { locationId } = getSessionInfo();

	return {
		filter: EDDocPath.payment_types,
		query_params: {
			type: EDDocFilterKeys.payment_types,
			locationId: locationId || '',
		},
	};
};

export const companySettingsFilter = (): IFilter => {
	return {
		filter: EDDocPath.company_settings,
		query_params: {} as any,
	};
};

export const customersFilter = (): IFilter => {
	const { locationId } = getSessionInfo();

	return {
		filter: EDDocPath.customer,
		query_params: {
			type: EDDocFilterKeys.customer,
			locationId: locationId || '',
		},
	};
};

export const salesFilter = (): IFilter => {
	const { locationId, userInfo } = getSessionInfo();

	return {
		filter: EDDocPath.sales,
		query_params: {
			type: EDDocFilterKeys.sales,
			locationId: locationId || '',
			cashierId: userInfo?._id || '',
			// isActive: "true",

			//todo: filter for name of the user propagating such action too, to avoid too much bombardment
		},
	};
};

export const optsSales = (filter: any) => {
	const r = {
		...(filter.filter &&
			filter.query_params.type &&
			filter.query_params.locationId &&
			filter.query_params.cashierId && {
				filter: filter.filter,
				query_params: filter.query_params,
				live: filter?.live ?? false,
				retry: true,
				// since: 0
			}),
	};
	return r;
};

export const optsProducts = (filter: any) => {
	const r = {
		...(filter.filter &&
			filter.query_params.type &&
			filter.query_params.locationId && {
				filter: filter.filter,
				query_params: filter.query_params,
				live: filter?.live ?? false,
				retry: true,
				// batch_size: 100,
				// batch_limit: 1_000,
				// back_off_function: function (delay: any) {
				//   if (delay === 0) return 10 ** 3;
				//   return delay * 3;
				// },
			}),
	};

	return r;
};

export const optsCashiers = (filter: any) => {
	return {
		...(filter.filter &&
			filter.query_params.locationId && {
				filter: filter.filter,
				query_params: filter.query_params,
				live: filter?.live ?? false,
				retry: true,
				batch_size: 100,
				batch_limit: 1_000,
			}),
	};
};

export const optsCustomer = (filter: any) => {
	return {
		...(filter.filter &&
			filter.query_params.locationId && {
				filter: filter.filter,
				query_params: filter.query_params,
				live: filter?.live ?? false,
				retry: true,
				batch_size: 100,
				batch_limit: 1_000,
			}),
	};
};

export const optsPaymentTypes = (filter: any) => {
	return {
		...(filter.filter &&
			filter.query_params.locationId && {
				filter: filter.filter,
				query_params: filter.query_params,
				live: filter?.live ?? false,
				retry: true,
				batch_size: 100,
				batch_limit: 1_000,
			}),
	};
};

export const optsChangesProducts = () => {
	return {
		live: true,
		since: 'now',
		include_docs: true,
	};
};

//export const optsChangesProducts = (filter: any) => {
//  return {
//    ...(filter.filter &&
//      filter.query_params.type &&
//      filter.query_params.location &&
//      filter.query_params.branch && {
//        filter: filter.filter,
//        query_params: filter.query_params,
//        live: true,
//        since: "now",
//        include_docs: true,
//      }),
//  };
//};
