import PouchDB from 'pouchdb';
import { getSessionInfo } from '../../utils/getSessionInfo';

export const remoteDB = (name: any) => {
	const { remoteUrl } = getSessionInfo();

	const remoteUrlNew = `${remoteUrl}${name && `/merchdb-${name}`}`;

	return new PouchDB(remoteUrlNew, {
		skip_setup: true,
		revs_limit: 1,
		auto_compaction: true,
	});
};

export const localDB = (name: string | undefined) => {
	const { userInfo, locationId } = getSessionInfo();

	if (!userInfo || !locationId) return new PouchDB();

	return new PouchDB(
		`${name && userInfo?.merchantName ? `${userInfo?.merchantName}-${userInfo?.profile?.email}-${name}` : 'error-db'}`,
		{
			skip_setup: true,
			revs_limit: 1,
			auto_compaction: true,
		}
	);
};

export const genericMerchantDB = (name: string | undefined) => {
	const { userInfo, locationId } = getSessionInfo();

	if (!userInfo || !locationId) return new PouchDB();

	return new PouchDB(`${userInfo?.merchantName}-${locationId}-${name}`, {
		skip_setup: true,
		revs_limit: 1,
		auto_compaction: true,
	});
};

export const cartDB = () => {
	const { userInfo, locationId } = getSessionInfo();

	if (!userInfo || !locationId) return new PouchDB();

	return new PouchDB(`${userInfo?.merchantName}-${locationId}-${userInfo?.profile?.email}-carts`, {
		skip_setup: true,
		revs_limit: 1,
		auto_compaction: true,
	});
};

export const salesDB = () => {
	const { userInfo, locationId } = getSessionInfo();

	if (!userInfo || !locationId) return new PouchDB();

	return new PouchDB(`${userInfo?.merchantName}-${locationId}-sales`, {
		skip_setup: true,
		revs_limit: 1,
		auto_compaction: true,
	});
};

export const inventoryDB = () => {
	const { userInfo, locationId } = getSessionInfo();

	if (!userInfo || !locationId) return new PouchDB();

	return new PouchDB(`${userInfo?.merchantName}-${locationId}-inventory`, {
		skip_setup: true,
		revs_limit: 1,
		auto_compaction: true,
	});
};

export const customerDB = () => {
	const { userInfo, locationId } = getSessionInfo();

	if (!userInfo || !locationId) return new PouchDB();

	return new PouchDB(`${userInfo?.merchantName}-${locationId}-customer`, {
		skip_setup: true,
		revs_limit: 1,
		auto_compaction: true,
	});
};

export const paymentTypesDB = () => {
	const { userInfo, locationId } = getSessionInfo();

	if (!userInfo || !locationId) return new PouchDB();

	return new PouchDB(`${userInfo?.merchantName}-${locationId}-payment_types`, {
		skip_setup: true,
		revs_limit: 1,
		auto_compaction: true,
	});
};

export const pinCodeDB = () => {
	const { userInfo, locationId } = getSessionInfo();

	if (!userInfo || !locationId) return new PouchDB();

	return new PouchDB(`${userInfo?.merchantName}-${locationId}-${userInfo?.profile?.email}-pincode`, {
		revs_limit: 1,
		auto_compaction: true,
		skip_setup: true,
	});
};
