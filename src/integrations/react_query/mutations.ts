import { useMutation } from 'react-query';
import { useQueryClient } from 'react-query';

import { shoppaLogin } from '../pouch/services/authService';
import { shoppaPinCode, verifyPasscode } from '../pouch/services/pincodeService';

import { triggerToast } from '../../toast/index';

import { useNavigate } from 'react-router-dom';

import { useAppDispatch } from '../../store/storeConfig';

import { CartProps, ECart, EQuerykeys, IDeleteCartItem, PaymentProps, SalesProps } from '../../models';

import {
	addToCart,
	updateCart,
	deleteCartItem,
	parkCartItem,
	unparkCartItem,
	discardCartItem,
	handlePayment,
	PaymentErrors,
	completeCartItem,
	markCartWithTransactionID,
	attachCustomerToCart,
	CustomerPayload,
	addCustomer,
	editCustomer,
	detachCustomerFromCart,
	attachSalesPerson,
	SalesPersonPayload,
	removePayment,
} from './queryFn';

import { handleDeleteCartItem } from './../pouch/algorithms';
import { convertWordUpperCase } from './../../utils/funcs';
import { saveSession } from '../../utils/storage';
import { handleCurrentProductId } from '../../store/slices/productSlice';
import { getSessionInfo } from '../../utils/getSessionInfo';

interface IuseLogin {
	username: string;
	password: string;
}

export const useLogin = () => {
	const navigate = useNavigate();

	return useMutation(({ username, password }: IuseLogin) => shoppaLogin(username, password, navigate), {
		onSuccess: async () => {
			triggerToast({
				type: 'success',
				message: 'Login Successful',
				duration: 3000,
			});
		},
		onError: async (err: any) => {
			triggerToast({
				type: 'error',
				message: err.message,
				duration: 5000,
			});

			return err.message;
		},
	});
};

export const usePinCode = () => {
	const navigate = useNavigate();

	return useMutation((passcode: string) => shoppaPinCode(passcode, navigate), {
		onSuccess: async () => {
			triggerToast({
				type: 'success',
				message: 'Pincode successfully set! Keep it save!',
				duration: 6000,
			});
		},
		onError: async (err: any) => {
			triggerToast({
				type: 'error',
				message: err.message || 'Something went wrong',
				duration: 5000,
			});
			return err.message;
		},
	});
};

export const useVerifyPasscode = () => {
	return useMutation((passcode: string) => verifyPasscode(passcode), {
		onSuccess: async () => {},
		onError: async (err: any) => {
			triggerToast({
				type: 'error',
				message: err.message || 'Something went wrong',
				duration: 5000,
			});
			return err.message;
		},
	});
};

export const useAddToCart = (handleAddingToCartStatus: (v: boolean) => void) => {
	const queryClient = useQueryClient();

	return useMutation((data: CartProps) => addToCart(data), {
		onMutate: async (values) => {
			queryClient.setQueryData([EQuerykeys.cart, ECart.active], values);

			handleAddingToCartStatus(true);
		},
		onSuccess: async ({ name, quantity }: any) => {
			// triggerToast({
			// 	type: 'success',
			// 	message: `Added ${quantity} piece${quantity > 1 ? 's' : ''} of ${name}`,
			// 	duration: 1000,
			// 	position: 'bottom-center',
			// });

			handleAddingToCartStatus(false);
		},
		onError: async () => {
			queryClient.invalidateQueries([EQuerykeys.cart, ECart.active]);
			triggerToast({
				type: 'error',
				message: 'Adding to cart failed!. Try again. here',
				duration: 2000,
				position: 'bottom-center',
			});

			handleAddingToCartStatus(false);
		},
	});
};

export type UpdateCartProps = {
	quantity: string | number;
	productId: string;
	variantId: number | null;
	refCartId: string;
};

export const useUpdateCart = (handleAddingToCartStatus: (v: boolean) => void, event?: 'change' | 'click') => {
	const queryClient = useQueryClient();

	const dispatch = useAppDispatch();

	return useMutation((data: SalesProps & { refCartId: string }) => updateCart(data, event), {
		onMutate: async (values) => {
			handleAddingToCartStatus(true);

			dispatch(handleCurrentProductId((values as any)?.productId));
		},
		onSuccess: async () =>
			// { name }: any

			{
				queryClient.invalidateQueries([EQuerykeys.cart, ECart.active]);
				// triggerToast({
				// 	type: 'success',
				// 	message: `Cart updated successfully for ${name}`,
				// 	duration: 1000,
				// 	position: 'bottom-center',
				// });

				handleAddingToCartStatus(false);
			},
		onError: async (err: any) => {
			const code = err?.code;

			const status = err?.status;

			//todo: if there's a 404 error then reset the session and start agains

			queryClient.invalidateQueries([EQuerykeys.cart, ECart.active]);
			triggerToast({
				type: code === 11 || status === 404 ? 'info' : 'error',
				message:
					code === 11 || status === 404
						? 'Encountered an error, initializing auto recovery... please wait'
						: 'Adding to cart failed!. Try again.',
				duration: 2000,
				position: 'bottom-center',
			});

			handleAddingToCartStatus(false);
		},
	});
};

export const useDeleteFromCart = () => {
	const queryClient = useQueryClient();

	return useMutation((data: IDeleteCartItem) => deleteCartItem(data), {
		onMutate: async ({ productId }: IDeleteCartItem) => {
			queryClient.setQueryData([EQuerykeys.cart, ECart.active], (currentCart: any) => {
				return { ...currentCart, products: handleDeleteCartItem(currentCart?.products, productId) };
			});
		},
		onError: async (name: string) => {
			queryClient.invalidateQueries(EQuerykeys.cart);

			triggerToast({
				type: 'error',
				message: `Failed to delete ${convertWordUpperCase(name)} failed!. Try again.`,
				duration: 2000,
				position: 'bottom-right',
			});
		},
		onSuccess: async (name: string) => {
			// triggerToast({
			// 	type: 'success',
			// 	message: `${convertWordUpperCase(name)} successfully deleted.`,
			// 	duration: 2000,
			// 	position: 'bottom-right',
			// });
		},
	});
};

export const useParkCart = () => {
	const queryClient = useQueryClient();

	return useMutation((note: string) => parkCartItem(note), {
		onError: async () => {
			queryClient.invalidateQueries([EQuerykeys.cart, ECart.active]);
			queryClient.invalidateQueries([EQuerykeys.cart, ECart.parked]);

			triggerToast({
				type: 'error',
				message: `Failed to park cart items!. Try again.`,
				duration: 2000,
				position: 'bottom-right',
			});
		},
		onSuccess: async () => {
			queryClient.invalidateQueries([EQuerykeys.cart, ECart.active]);
			queryClient.invalidateQueries([EQuerykeys.cart, ECart.parked]);

			triggerToast({
				type: 'success',
				message: `Cart items successfully parked!`,
				duration: 2000,
				position: 'bottom-right',
			});
		},
	});
};

export const useDiscardCart = () => {
	const queryClient = useQueryClient();

	return useMutation((note: string) => discardCartItem(note), {
		onError: async () => {
			queryClient.invalidateQueries([EQuerykeys.cart, ECart.active]);
			queryClient.invalidateQueries([EQuerykeys.cart, ECart.parked]);

			triggerToast({
				type: 'error',
				message: `Failed to park cart items!. Try again.`,
				duration: 2000,
				position: 'bottom-right',
			});
		},
		onSuccess: async () => {
			queryClient.invalidateQueries([EQuerykeys.cart, ECart.active]);
			queryClient.invalidateQueries([EQuerykeys.cart, ECart.parked]);

			triggerToast({
				type: 'success',
				message: `Cart items successfully discarded!`,
				duration: 2000,
				position: 'bottom-right',
			});
		},
	});
};

export const useCompleteCart = () => {
	const queryClient = useQueryClient();

	return useMutation((note: string) => completeCartItem(note), {
		onMutate: async () => {
			// await queryClient.cancelQueries([EQuerykeys.cart, ECart.parked]);
		},
		onError: async () => {
			queryClient.invalidateQueries([EQuerykeys.cart, ECart.active]);
			queryClient.invalidateQueries([EQuerykeys.cart, ECart.parked]);

			triggerToast({
				type: 'error',
				message: `Failed to initiate next sales, try again.`,
				duration: 2000,
				position: 'bottom-right',
			});
		},
		onSuccess: async () => {
			queryClient.invalidateQueries([EQuerykeys.cart, ECart.active]);
			queryClient.invalidateQueries([EQuerykeys.cart, ECart.parked]);

			// triggerToast({
			// 	type: 'success',
			// 	message: `Sales successfully made!`,
			// 	duration: 2000,
			// 	position: 'bottom-right',
			// });
		},
	});
};

export const useUnParkCart = () => {
	const queryClient = useQueryClient();

	return useMutation((cartId: string) => unparkCartItem(cartId), {
		onMutate: async () => {
			// await queryClient.cancelQueries([EQuerykeys.cart, ECart.parked]);
		},
		onError: async () => {
			queryClient.invalidateQueries([EQuerykeys.cart, ECart.parked]);

			triggerToast({
				type: 'error',
				message: `Failed to unpark cart items!. Try again.`,
				duration: 2000,
				position: 'bottom-right',
			});
		},
		onSuccess: async (successId: string) => {
			const { cartId, userInfo } = getSessionInfo();

			saveSession(cartId + `-${userInfo?.profile?.email}`, successId);

			queryClient.invalidateQueries([EQuerykeys.cart, ECart.active]);
			queryClient.invalidateQueries([EQuerykeys.cart, ECart.parked]);

			triggerToast({
				type: 'success',
				message: `Cart items successfully unparked!`,
				duration: 2000,
				position: 'bottom-center',
			});
		},
	});
};

export const useMarkCartWithTransactionID = () => {
	const queryClient = useQueryClient();

	return useMutation((transactionId: string) => markCartWithTransactionID(transactionId), {
		onError: async () => {
			queryClient.invalidateQueries([EQuerykeys.cart, ECart.active]);

			triggerToast({
				type: 'error',
				message: `Failed to unpark cart items!. Try again.`,
				duration: 2000,
				position: 'bottom-right',
			});
		},
		onSuccess: async () => {
			queryClient.invalidateQueries([EQuerykeys.cart, ECart.active]);

			// triggerToast({
			// 	type: 'success',
			// 	message: `Cart successfully mark with transaction ID`,
			// 	duration: 2000,
			// 	position: 'bottom-right',
			// });
		},
	});
};

export const usePayment = () => {
	const queryClient = useQueryClient();

	return useMutation(({ method, amountPaid, note, paymentProvider }: PaymentProps) => handlePayment({ method, amountPaid, note, paymentProvider}), {
		onError: async (error) => {
			queryClient.invalidateQueries([EQuerykeys.cart, ECart.active]);

			const errorInstance = error as Error;

			if (errorInstance?.message === PaymentErrors.amount_greater) return;

			triggerToast({
				type: 'error',
				message: `Failed to make payment for this cart!. Try again.`,
				duration: 2000,
				position: 'bottom-right',
			});
		},
		onSuccess: async () => {
			queryClient.invalidateQueries([EQuerykeys.cart, ECart.active]);

			// triggerToast({
			// 	type: 'success',
			// 	message: `Payment successful!`,
			// 	duration: 2000,
			// 	position: 'bottom-right',
			// });
		},
	});
};

export const useRemovePayment = () => {
	const queryClient = useQueryClient();

	return useMutation((id: string | number) => removePayment(id), {
		onError: async (error) => {
			queryClient.invalidateQueries([EQuerykeys.cart, ECart.active]);

			const errorInstance = error as Error;

			if (errorInstance?.message === PaymentErrors.amount_greater) return;

			triggerToast({
				type: 'error',
				message: `Failed to remove payment for this cart!. Try again.`,
				duration: 2000,
				position: 'bottom-right',
			});
		},
		onSuccess: async () => {
			queryClient.invalidateQueries([EQuerykeys.cart, ECart.active]);
		},
	});
};

export const useAttachCustomerToCart = () => {
	const queryClient = useQueryClient();

	return useMutation(
		({ id, lastName, firstName, email, phoneNumber, birthday }: CustomerPayload) =>
			attachCustomerToCart({ firstName, lastName, id, email, phoneNumber, birthday }),
		{
			onError: async () => {
				queryClient.invalidateQueries([EQuerykeys.cart, ECart.active]);

				triggerToast({
					type: 'error',
					message: `Failed to add customer. Try again`,
					duration: 2000,
					position: 'bottom-right',
				});
			},
			onSuccess: async (data: any) => {
				queryClient.invalidateQueries([EQuerykeys.cart, ECart.active]);

				const { firstName, lastName } = data ?? {};

				firstName &&
					lastName &&
					triggerToast({
						type: 'success',
						message: `${lastName} ${firstName} added to this cart.`,
						duration: 2000,
						position: 'bottom-right',
					});
			},
		}
	);
};

export const useDetachCustomerFromCart = () => {
	const queryClient = useQueryClient();

	return useMutation(() => detachCustomerFromCart(), {
		onError: async () => {
			queryClient.invalidateQueries([EQuerykeys.cart, ECart.active]);

			triggerToast({
				type: 'error',
				message: `Failed to add customer. Try again`,
				duration: 2000,
				position: 'bottom-right',
			});
		},
		onSuccess: async ({ name }: any) => {
			queryClient.invalidateQueries([EQuerykeys.cart, ECart.active]);

			triggerToast({
				type: 'success',
				message: `${name} removed from this cart.`,
				duration: 2000,
				position: 'bottom-right',
			});
		},
	});
};

export const useAddCustomer = () => {
	const queryClient = useQueryClient();

	return useMutation((payload: Omit<CustomerPayload, 'id'>) => addCustomer(payload), {
		onError: async (err) => {
			const error = err as Error;

			queryClient.invalidateQueries([EQuerykeys.cart, ECart.active]);

			queryClient.invalidateQueries([EQuerykeys.customer]);

			triggerToast({
				type: 'error',
				message: error.message ? error.message : `Failed to create customer. Try again`,
				duration: 2000,
				position: 'bottom-right',
			});
		},
		onSuccess: async ({ firstName, lastName }: any) => {
			triggerToast({
				type: 'success',
				message: `${firstName} ${lastName} successfully created.`,
				duration: 2000,
				position: 'bottom-right',
			});

			queryClient.invalidateQueries([EQuerykeys.cart, ECart.active]);

			queryClient.invalidateQueries([EQuerykeys.customer]);
		},
	});
};

export const useEditCustomer = () => {
	const queryClient = useQueryClient();

	return useMutation((payload: CustomerPayload) => editCustomer(payload), {
		onError: async () => {
			queryClient.invalidateQueries([EQuerykeys.cart, ECart.active]);

			queryClient.invalidateQueries([EQuerykeys.customer]);

			triggerToast({
				type: 'error',
				message: `Failed to edit customer. Try again`,
				duration: 2000,
				position: 'bottom-right',
			});
		},
		onSuccess: async () => {
			queryClient.invalidateQueries([EQuerykeys.cart, ECart.active]);

			queryClient.invalidateQueries([EQuerykeys.customer]);

			triggerToast({
				type: 'success',
				message: `Customer successfully edited.`,
				duration: 2000,
				position: 'bottom-right',
			});
		},
	});
};

export const useAttachSalesPercon = () => {
	const queryClient = useQueryClient();

	return useMutation((payload: SalesPersonPayload) => attachSalesPerson(payload), {
		onError: async () => {
			queryClient.invalidateQueries([EQuerykeys.cart, ECart.active]);

			triggerToast({
				type: 'error',
				message: `Failed to add sales person. Try again`,
				duration: 2000,
				position: 'bottom-right',
			});
		},
		onSuccess: async () => {
			queryClient.invalidateQueries([EQuerykeys.cart, ECart.active]);
		},
	});
};
