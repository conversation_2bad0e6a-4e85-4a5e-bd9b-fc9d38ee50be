import { UseQueryOptions, useQuery } from 'react-query';

import {
	getCurrentCart,
	pullCashiers,
	pullCustomers,
	// pullFromCart,
	pullParkedSales,
	pullProducts,
} from './queryFn';
import { FlattenedProductProps } from '../../models';
import { pullPaymentTypes } from './queryFn';

export const useGetProducts = (
	key: string[],
	options?:
		| Omit<UseQueryOptions<FlattenedProductProps[], unknown, FlattenedProductProps[], string[]>, 'queryKey' | 'queryFn'>
		| undefined
) => {
	return useQuery(key, pullProducts, options);
};

export const useGetCart = (key: string[]) => {
	return useQuery(
		key,
		// pullFromCart
		getCurrentCart
	);
};

export const useGetCustomer = (key: string[]) => {
	return useQuery(key, pullCustomers);
};

export const useGetCashiers = (key: string[]) => {
	return useQuery(key, pullCashiers);
};

export const useGetParkedCart = (key: string[]) => {
	return useQuery(key, pullParkedSales);
};

export const useGetPaymentTypes = (key: string[]) => {
	return useQuery(key, pullPaymentTypes);
};
