import { EQuerykeys } from "../../models/index";
import { handleDeleted } from "../pouch/algorithms";
import { handleUpdatedOrInserted } from "./../pouch/algorithms";

//export const pqClient = ({
//  queryClient,
//  setIsReplicating,
//  name,
//  dispatch,
//}: any) => {
//  const currentData = queryClient.getQueryData(EQuerykeys.product);
//  if (!currentData || !currentData?.length) {
//    genericMerchantDB(name)
//      .allDocs({ include_docs: true })
//      .then((res: any) => {
//        queryClient.setQueryData(
//          EQuerykeys.product,
//          res.rows.map((row: any) => row.doc)
//        );
//      })
//      .catch((err) => console.log(err, "from generic"));
//  } else {
//    setIsReplicating(false);
//  }
//};

export const productqClient = (queryClient: any, res: any) => {

  try {
    queryClient.setQueryData(
      [EQuerykeys.product],
      res.rows.map((row: any) => row.doc)
    );
  }
  catch (err: any) {
    console.log(err)
  }
};

//removed async

export const productChangesqClient = (queryClient: any, change: any) => {
  try {
    if (change.deleted) {
      queryClient.setQueryData(EQuerykeys.product, (previousData: any) =>
        handleDeleted(previousData, change.id)
      );
    } else {
      queryClient.setQueryData(EQuerykeys.product, (previousData: any) =>
        handleUpdatedOrInserted(previousData, change.docs)
      );
    }
  } catch (err) {
    console.log(err);
  }
};

export const salesChangesqClient = async (queryClient: any, change: any) => {
  try {
    if (change.deleted) {
      queryClient.setQueryData(EQuerykeys.sales, (previousData: any) =>
        handleDeleted(previousData, change.id)
      );
    } else {
      queryClient.setQueryData(EQuerykeys.sales, (previousData: any) =>
        handleUpdatedOrInserted(previousData, change.doc)
      );
    }
  } catch (err) {
    console.log(err);
  }
};

