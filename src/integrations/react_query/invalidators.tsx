import { QueryClient } from 'react-query';
import { EQuerykeys } from '../../models';

export const invalidators = (queryClient: QueryClient) => {
	const invalidatorPhases: Record<EQuerykeys, () => Promise<void>> = {
		[EQuerykeys.product]: () => queryClient.invalidateQueries([EQuerykeys.product]),
		[EQuerykeys.cashier]: () => queryClient.invalidateQueries([EQuerykeys.cashier]),
		[EQuerykeys.customer]: () => queryClient.invalidateQueries([EQuerykeys.customer]),
		[EQuerykeys.cart]: () => queryClient.invalidateQueries([EQuerykeys.cart]),
		[EQuerykeys.sales]: () => queryClient.invalidateQueries([EQuerykeys.sales]),
		[EQuerykeys.payment_types]: () => queryClient.invalidateQueries([EQuerykeys.payment_types]),
	};
	return invalidatorPhases;
};
