import {
	CartProps,
	ECart,
	FlattenedProductProps,
	IDeleteCartItem,
	PaymentProps,
	ProductItemProps,
	SalesProps,
	SalesStatus,
} from '../../models';

import { cartDB, customerDB, genericMerchantDB, salesDB } from './../pouch/DBConn';

import { handleUpdateCart } from '../pouch/algorithms';
import { handleDeleteCartItem } from './../pouch/algorithms';
import { removeSession } from '../../utils/storage';
import { triggerToast } from '../../toast';
import { getSessionInfo } from '../../utils/getSessionInfo';
import { flattenedProductData } from '../../utils/composeProducts';
import { UserResponseProps } from '../../types';
import { generateTransactionId } from '../../utils/generateTransactionId';
import { paymentTypesDB } from '../pouch/DBConn';

//todo: examine db instance creation

export const pullProducts = async (): Promise<FlattenedProductProps[]> => {
	try {
		//todo: implement enums here
		const res = await genericMerchantDB('products').allDocs<ProductItemProps>({ include_docs: true });

		// const docs = res.rows.map((row) => (row.doc && !row._deleted ? row.doc : null)).filter(Boolean)

		const docs = res.rows.map((row) => row.doc) as ProductItemProps[];

		return flattenedProductData(docs) as FlattenedProductProps[];
	} catch (err: unknown) {
		if (err instanceof Error) {
			throw new Error(err.message);
		}

		throw err;
	}
};

export const getPendingAndProcessingSales = async () => {
	try {
		const res = await salesDB().allDocs({ include_docs: true });
		const docs = res.rows.map((row) => row.doc);

		const r = docs.filter((doc: any) => doc.status === SalesStatus.pending || doc.status === SalesStatus.processing);

		return r;
	} catch (err: any) {
		throw new Error(err);
	}
};

export const getAllSales = async () => {
	try {
		const res = await salesDB().allDocs({ include_docs: true });

		return res.rows.map((row) => row.doc);
	} catch (err: any) {
		throw new Error(err);
	}
};

export const addToCart = async (item: CartProps) => {
	try {
		await cartDB().put(item);

		return item?.products?.[0];
	} catch (err: any) {
		throw new Error(err);
	}
};

export const pullCustomers = async () => {
	try {
		const res = await customerDB().allDocs({ include_docs: true, descending: true });
		return res.rows?.map((d) => d?.doc);
	} catch (err: any) {
		throw new Error(err);
	}
};

export const pullPaymentTypes = async () => {
	try {
		const res = await paymentTypesDB().allDocs({ include_docs: true, descending: true });
		console.log({ res });
		return res.rows?.map((d) => d?.doc)?.filter((doc: any) => doc?.isActive);
	} catch (err: any) {
		throw new Error(err);
	}
};

export const pullCashiers = async () => {
	try {
		const res = await genericMerchantDB('cashiers').allDocs<UserResponseProps>({
			include_docs: true,
			descending: true,
		});
		return res.rows?.map((d) => d?.doc) as UserResponseProps[];
	} catch (err: any) {
		throw new Error(err);
	}
};

export const pullParkedSales = async () => {
	try {
		const res = await cartDB().allDocs({ include_docs: true, descending: true });

		return res.rows.map((row: any) => (row.doc?.status === ECart.parked ? row.doc : null))?.filter(Boolean);
	} catch (err: any) {
		throw new Error(err);
	}
};

export const pullActiveCart = async () => {
	try {
		const res = await cartDB().allDocs<{ [key: string]: any }>({ include_docs: true });

		const k = res.rows.find((row: any) => row.doc?.status === ECart.active);

		return k?.doc;
	} catch (err: any) {
		throw new Error(err);
	}
};

//TODO: remove id cart sessionx
export const updateCart = async (values: SalesProps & { refCartId: string }, event?: 'change' | 'click') => {
	try {
		//todo: in case this can not be found, this throws an error (not found error)
		//add normally to cart in this situation
		const doc = await cartDB().get<CartProps>(values.refCartId);

		//clear the refcart session if no doc was found
		if (!doc) return;

		const currentCart = doc.products;

		const items = handleUpdateCart(currentCart, values, event);

		if (!items) {
			doc.products = [values, ...doc.products];
			await cartDB().put(doc);
			return;
		}

		doc.products = items;
		doc.updatedAt = new Date().toISOString();
		await cartDB().put(doc);

		// if (handleUpdateCart(currentCart, values, event)) {
		// 	const items = handleUpdateCart(currentCart, values, event);
		// 	if (!items) return;
		// 	doc.products = items;
		// 	doc.updatedAt = new Date().toISOString(;)
		// 	await cartDB().put(doc);
		// } else {
		// 	doc.products = [values, ...doc.products];
		// 	await cartDB().put(doc);
		// }

		return values;
	} catch (err: any) {
		const { userInfo, cartId } = getSessionInfo();

		if (err?.status === 404) {
			removeSession(`${cartId}-${userInfo?.profile?.email}`);
			setTimeout(() => {
				window.location.reload();
			}, 0);
			throw err;
		}

		if (err?.code === 11) {
			//needs to reinit
			removeSession(`${cartId}-${userInfo?.profile?.email}`);

			setTimeout(() => {
				window.location.reload();
			}, 1000);

			throw err;
		}
		throw new Error(err);
	}
};

export const deleteCartItem = async ({ refCartId, productId, name }: IDeleteCartItem) => {
	try {
		const doc: any = await cartDB().get(refCartId);

		const currentCartItems = doc.products;

		const cartItemsAfterDeletion = handleDeleteCartItem(currentCartItems, productId);

		doc.products = cartItemsAfterDeletion;

		await cartDB().put(doc);

		return name;
	} catch (err: any) {
		throw new Error(err);
	}
};

export const parkCartItem = async (note: string) => {
	const { refCartId, userInfo, cartId } = getSessionInfo();

	try {
		const doc: any = await cartDB().get(refCartId);

		doc.status = ECart.parked;
		doc.note = note;
		doc.updatedAt = new Date().toISOString();

		await cartDB().put(doc);

		try {
			removeSession(`${cartId}-${userInfo?.profile?.email}`);
		} catch (e) {}

		return 'Success';
	} catch (err: any) {
		throw new Error(err);
	}
};

export const getCurrentCart = async (): Promise<
	(CartProps & { payments?: PaymentProps[]; transactionId?: number }) | undefined
> => {
	const { refCartId } = getSessionInfo();

	if (!refCartId) return undefined;

	try {
		return await cartDB().get(refCartId);
	} catch (err: any) {
		if (err?.status === 404) {
			return undefined;
		}
		throw new Error(err);
	}
};

export const discardCartItem = async (note: string) => {
	const { refCartId, cartId, userInfo } = getSessionInfo();

	try {
		const doc: any = await cartDB().get(refCartId);

		doc.status = ECart.discarded;
		doc.note = note;
		doc.updatedAt = new Date().toISOString();

		await cartDB().put(doc);

		try {
			removeSession(`${cartId}-${userInfo?.profile?.email}`);
		} catch (e) {}

		return 'Success';
	} catch (err: any) {
		throw new Error(err);
	}
};

export const completeCartItem = async (note: string) => {
	const { refCartId, cartId, userInfo } = getSessionInfo();

	try {
		const doc: any = await cartDB().get(refCartId);

		doc.status = ECart.completed;
		doc.note = note ?? null;
		doc.updatedAt = new Date().toISOString();

		await cartDB().put(doc);

		try {
			removeSession(`${cartId}-${userInfo?.profile?.email}`);
		} catch (err) {}

		return 'Success';
	} catch (err: any) {
		console.log(err);
	}
};

export const markCartWithTransactionID = async (transactionId: string) => {
	const { refCartId } = getSessionInfo();

	try {
		const doc: any = await cartDB().get(refCartId);

		doc.transactionId = transactionId;

		await cartDB().put(doc);

		return transactionId;
	} catch (err: any) {
		throw new Error(err);
	}
};

export const unparkCartItem = async (cartId: string) => {
	const { refCartId } = getSessionInfo();

	try {
		if (refCartId) {
			const currentCart = await cartDB().get<CartProps>(refCartId);
			currentCart.status = ECart.parked;
			currentCart.updatedAt = new Date().toISOString();
			currentCart.note =
				currentCart.note === '' || currentCart.note
					? currentCart.note
					: !currentCart.note
						? 'automatically parked'
						: '';

			await cartDB().put(currentCart);
		}

		const doc = await cartDB().get<CartProps>(cartId);

		doc.status = ECart.active;
		doc.updatedAt = new Date().toISOString();

		await cartDB().put(doc);

		return cartId;
	} catch (err: any) {
		throw new Error(err);
	}
};

export enum PaymentErrors {
	amount_greater = 'greater than error',
}

export const handlePayment = async ({ method, amountPaid, note, paymentProvider }: PaymentProps) => {
	const { refCartId, cartId } = getSessionInfo();

	try {
		const doc = ((await cartDB().get(refCartId)) as CartProps & { payments: PaymentProps[] }) || undefined;

		if (!doc) throw 'no cart item found';

		if (!doc.payments) {
			doc.payments = [];
		}

		const paymentDatails = {
			id: Date.now(),
			method,
			amountPaid,
			note,
			paymentProvider: paymentProvider || null,
		};

		doc.payments.push(paymentDatails);

		doc.updatedAt = new Date().toISOString();

		await cartDB().put(doc);

		return cartId;
	} catch (err: any) {
		throw new Error(err);
	}
};

export const removePayment = async (id: string | number) => {
	const { refCartId, cartId } = getSessionInfo();

	try {
		const doc = ((await cartDB().get(refCartId)) as CartProps & { payments: PaymentProps[] }) || undefined;

		if (!doc) throw 'no cart item found';

		if (!doc.payments) {
			doc.payments = [];
		}

		doc.payments = doc.payments?.filter((payment) => payment?.id?.toString() !== id?.toString());

		doc.updatedAt = new Date().toISOString();

		await cartDB().put(doc);

		return cartId;
	} catch (err: any) {
		throw new Error(err);
	}
};

export type CustomerPayload = {
	firstName: string;
	lastName: string;
	id: string;
	phoneNumber: string;
	email: string;
	birthday?: {
		month: string;
		day: string;
	};
};

export const attachCustomerToCart = async ({
	firstName,
	lastName,
	id,
	email,
	phoneNumber,
	birthday,
}: CustomerPayload) => {
	const { refCartId } = getSessionInfo();

	if (!refCartId) {
		triggerToast({
			message: "You can't add customer to an empty cart",
			type: 'info',
			position: 'bottom-right',
		});

		return {};
	}

	try {
		const doc: CartProps = await cartDB().get(refCartId);

		doc.customer = {
			name: `${lastName} ${firstName}`,
			id,
			phoneNumber: phoneNumber || null,
			email: email || null,
			birthday: birthday || null,
		};

		doc.updatedAt = new Date().toISOString();

		await cartDB().put(doc);

		return { firstName, lastName };
	} catch (err: any) {
		throw new Error(err);
	}
};

export const detachCustomerFromCart = async () => {
	const { refCartId } = getSessionInfo();

	if (!refCartId) {
		return;
	}

	try {
		const doc: any = await cartDB().get(refCartId);

		const copiedCustomer = { ...(doc.customer ?? {}) };

		doc.customer = null;

		doc.updatedAt = new Date().toISOString();

		await cartDB().put(doc);

		return copiedCustomer;
	} catch (err: any) {
		throw new Error(err);
	}
};

export const addCustomer = async (payload: Omit<CustomerPayload, 'id'>) => {
	try {
		await customerDB().createIndex({
			index: {
				fields: ['email', 'phoneNumber'],
			},
		});

		const existingCustomer = await customerDB().find({
			selector: {
				$or: [
					{
						email: { $regex: new RegExp(payload.email, 'i') },
					},
					{
						phoneNumber: payload.phoneNumber,
					},
				],
			},
		});

		if (existingCustomer.docs?.length > 0) {
			throw 'user already exist';
		}

		const { userLocationObject } = getSessionInfo();

		//TODO: change this to UUID
		const res = await customerDB().put({
			_id: `CU-${generateTransactionId()}`,
			...payload,
			updatedAt: new Date().toISOString(),
			createdAt: new Date().toISOString(),
			type: 'customer',
			assignedLocation: userLocationObject,
		});

		try {
			const currentCart = await getCurrentCart();

			if (Object.values(currentCart ?? {})?.length > 0) {
				await attachCustomerToCart({ ...payload, id: res.id });
			}
		} catch (err) {
			//allow to fail silently
		}

		return payload;
	} catch (err: any) {
		throw new Error(typeof err === 'string' ? err : 'error adding customer, try again');
	}
};

const getCartAndEditCustomer = async (
	customerId: string,
	{ firstName, lastName, email, id, phoneNumber, birthday }: CustomerPayload
) => {
	const { refCartId } = getSessionInfo();

	try {
		const doc: any = await cartDB().get(refCartId);

		if (!doc.customer) return;

		if (doc.customer?.id === customerId) {
			try {
				doc.customer = {
					name: `${lastName} ${firstName}`,
					id,
					phoneNumber: phoneNumber || null,
					email: email || null,
					birthday: birthday || null,
				};
				await cartDB().put(doc);
			} catch (e) {
				throw new Error('Error editing cart');
			}
		}
	} catch (e: any) {
		if (e?.status === 404) return {};
		throw new Error(e);
	}
	return void 0;
};

export const editCustomer = async (payload: CustomerPayload) => {
	try {
		const customerData = await customerDB().get(payload.id);

		Promise.all([
			await customerDB().put({
				...customerData,
				...payload,
			}),
			await getCartAndEditCustomer(payload?.id, payload),
		]);

		return payload;
	} catch (err: any) {
		throw new Error(err);
	}
};

export type SalesPersonPayload = {
	firstName: string;
	lastName: string;
	id: string;
};

export const attachSalesPerson = async (payload: SalesPersonPayload) => {
	const { refCartId } = getSessionInfo();

	if (!refCartId) {
		return;
	}

	try {
		const doc: any = await cartDB().get(refCartId);

		doc.salesPerson = payload;

		doc.updatedAt = new Date().toISOString();

		await cartDB().put(doc);

		return doc;
	} catch (err: any) {
		throw new Error(err);
	}
};
