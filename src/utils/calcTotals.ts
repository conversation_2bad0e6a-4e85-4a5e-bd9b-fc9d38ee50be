import { PaymentProps, SalesProps } from '../models';
import bigDecimal from 'js-big-decimal';
import { getSessionInfo } from './getSessionInfo';
import { PaymentTypes } from '../types/index';

const calculateTotalAmount = (items: SalesProps[]): string => {
	return items
		.reduce(
			(total, item) =>
				total.add(new bigDecimal(item.sellingPrice ?? '0').multiply(new bigDecimal(item.quantity ?? '1'))),
			new bigDecimal('0')
		)
		.round(2)
		.getValue();
};

const calculateTotalPayableAmount = (items: SalesProps[]) => {
	const totalAmountWithDiscount = calculateTotalAmountWithDiscount(items);
	const vatAmount = calculateTotalTaxAmount(items);

	return new bigDecimal(totalAmountWithDiscount).add(new bigDecimal(vatAmount)).round(2).getValue();
};

export const calculateTotalAmountWithDiscount = (items: SalesProps[]): string => {
	return items
		.reduce((acc, { discount, sellingPrice, quantity, isDiscountValid }) => {
			const safeSellingPrice = new bigDecimal(sellingPrice ?? 0);

			const safeDiscount = isDiscountValid ? new bigDecimal(discount ?? 0) : new bigDecimal(0);

			const safeQuantity = new bigDecimal(quantity ?? 1);

			const discountedPrice = safeSellingPrice.subtract(safeDiscount);
			return acc.add(discountedPrice.multiply(safeQuantity));
		}, new bigDecimal('0'))
		.round(2)
		.getValue();
};


export const calculateTotalTaxAmount = (items: SalesProps[]): string => {
	const totalAmountWithDiscount = new bigDecimal(calculateTotalAmountWithDiscount(items));
	const taxDecimal = calculateTotalTax();

	return totalAmountWithDiscount.multiply(taxDecimal).round(2).getValue();
};

const calculateTotalDiscount = (items: SalesProps[]): string => {
	return items
		.reduce(
			(total, item) => {
				if (item?.isDiscountValid) {
					return total.add(new bigDecimal(item.discount ?? '0').multiply(new bigDecimal(item.quantity ?? '1')));
				}
				return total;
			},
			new bigDecimal('0')
		)
		.round(2)
		.getValue();
};


const calculateTotalTax = () => {
	const { companyInfo } = getSessionInfo();

	return new bigDecimal(+(companyInfo?.tax || 0) / 100);
};

const calculateTotalPayments = (payments: PaymentProps[]): string => {
	const total = payments.reduce((total, payment) => {
		return total.add(new bigDecimal(payment.amountPaid ?? 0));
	}, new bigDecimal('0'));

	return total.round(2).getValue();
};

const calculateTotalAmountWithTax = (items: SalesProps[]): string => {
	const totalAmount = items.reduce((total, { sellingPrice, quantity }) => {
		const safeSellingPrice = new bigDecimal(sellingPrice ?? 0);
		const safeQuantity = new bigDecimal(quantity ?? 1);
		return total.add(safeSellingPrice.multiply(safeQuantity));
	}, new bigDecimal('0'));

	const taxDecimal = calculateTotalTax();
	const totalWithTax = totalAmount.add(totalAmount.multiply(taxDecimal));

	return totalWithTax.round(2).getValue();
};


export const calculateOutstandingAmountWhenCreditOrPaid = (totalAmount: string, amountPaid: string, payments: PaymentProps[]): string => {

	const hasCredit = payments?.some((payment) => payment?.method === PaymentTypes.credit);

	const allIsPaidOnCredit = payments?.every((payment) => payment?.method === PaymentTypes?.credit);

	if (hasCredit) {
		const paidWithoutCredit = payments?.filter?.((payment) => payment?.method === PaymentTypes.cash || PaymentTypes.card)?.reduce((total, payment) => {
			return total.add(new bigDecimal(payment.amountPaid ?? 0));
		}, new bigDecimal('0'));

		const paidOnCredit = payments?.filter?.((payment) => payment?.method === PaymentTypes.credit)?.reduce((total, payment) => {
			return total.add(new bigDecimal(payment.amountPaid ?? 0));
		}, new bigDecimal('0'));


		const totalWithTax = new bigDecimal(totalAmount).add(new bigDecimal(totalAmount).multiply(calculateTotalTax()));

		const outstanding = totalWithTax.subtract(paidWithoutCredit).add(paidOnCredit);

		return outstanding.compareTo(new bigDecimal('0')) > 0 ? outstanding.round(2).getValue() : '0';
	}

	if (allIsPaidOnCredit) {
		const totalWithTax = new bigDecimal(totalAmount).add(new bigDecimal(totalAmount).multiply(calculateTotalTax()));

		const outstanding = totalWithTax;

		return outstanding.compareTo(new bigDecimal('0')) > 0 ? outstanding.round(2).getValue() : '0';
	}

	const totalWithTax = new bigDecimal(totalAmount).add(new bigDecimal(totalAmount).multiply(calculateTotalTax()));

	const outstanding = totalWithTax.subtract(new bigDecimal(amountPaid));


	return outstanding.compareTo(new bigDecimal('0')) > 0 ? outstanding.round(2).getValue() : '0';
};

const calculateOutstandingAmountWithTax = (totalAmount: string, amountPaid: string): string => {
	const totalWithTax = new bigDecimal(totalAmount).add(new bigDecimal(totalAmount).multiply(calculateTotalTax()));

	const outstanding = totalWithTax.subtract(new bigDecimal(amountPaid));

	return outstanding.compareTo(new bigDecimal('0')) > 0 ? outstanding.round(2).getValue() : '0';
};

const calculateOverpaidAmount = (totalAmount: string, amountPaid: string): string => {

	const totalWithTax = new bigDecimal(totalAmount).add(new bigDecimal(totalAmount).multiply(calculateTotalTax()));
	const overpaidAmount = new bigDecimal(amountPaid).subtract(totalWithTax);

	return overpaidAmount.compareTo(new bigDecimal('0')) > 0 ? overpaidAmount.round(2).getValue() : '0';
};

export {
	calculateOutstandingAmountWithTax,
	calculateOverpaidAmount,
	calculateTotalAmount,
	calculateTotalDiscount,
	calculateTotalPayments,
	calculateTotalTax,
	calculateTotalAmountWithTax,
	calculateTotalPayableAmount,
};
