import { pinCodeDB } from '../integrations/pouch/DBConn';
import { handleShowPinCodeScreen } from '../store/slices/authSlice';
import store from '../store/storeConfig';

export const createPinCodeStatus = async (status: boolean) => {
	try {
		const r = await pinCodeDB().putIfNotExists({
			_id: 'pincode',
			status,
		});
		return r;
	} catch (err: any) {
		throw new Error(err);
	}
};

export const updatePinCodeStatus = async (status: boolean) => {
	try {
		const currentDoc = await pinCodeDB().get('pincode');

		const r = await pinCodeDB().put({
			...currentDoc,
			status,
		});
		return r;
	} catch (err: any) {
		if (err?.status === 409) {
			return;
		}
		throw new Error(err);
	}
};

export const getPinCodeStatus = async () => {
	try {
		const res = await pinCodeDB().get<{ status: boolean }>('pincode');

		return res.status;
	} catch (err: any) {
		throw new Error(err);
	}
};

export const trackPinCodeStatus = async () => {
	try {
		// await createPinCodeStatus(false);

		const status = await getPinCodeStatus();
		if (status === true) {
			store.dispatch(handleShowPinCodeScreen(false));
		} else {
			store.dispatch(handleShowPinCodeScreen(true));
		}
	} catch (err: any) {
		throw new Error(err);
	}
};
