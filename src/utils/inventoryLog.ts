import { v4 as uuidv4 } from 'uuid';
import { UserResponseProps } from '../types';

export type PurchaseItemsProps = {
    productId: string;
    quantity: number;
    sellingPrice: number | null;
    discount: number | null;
    unitCost: number | null;
    totalAmount: number;
    variantId?: string | null;
};

export type TransactionTypes = 'stockOut' | 'stockIn';

export type TransactionStatuses =
    | 'purchase'
    | 'return'
    | 'adjustment'
    | 'sales'
    | 'transfer'
    | 'setup';


export const composeSingleInventoryLog = ({
    purchaseItem,
    transactionType,
    referenceTransactionId,
    referenceTransactionNo,
    transactionStatus,
    variantId,
    userInfo,
    note,
    reason,
    productName,
    refCartId,
    adjustmentDate,
    images,
    externalReferenceNumber,
    assignedLocation,
    fromLocation,
    toLocation,
    sourceCreatedDate,
    userFullName,
    userIdentity
}: {
    purchaseItem: PurchaseItemsProps;
    transactionType: TransactionTypes;
    transactionStatus: TransactionStatuses;
    referenceTransactionId: string | null;
    referenceTransactionNo: string | null;
    variantId?: string;
    userInfo: UserResponseProps | null;
    note?: string | null;
    reason?: string | null;
    assignedLocation?: UserResponseProps['assignedLocations'][0];
    productName: string | null;
    refCartId: string | null;
    adjustmentDate?: string | null;
    images?: string[] | null;
    externalReferenceNumber?: string | null;
    // processed?: boolean;
    fromLocation?: UserResponseProps['assignedLocations'][0];
    toLocation?: UserResponseProps['assignedLocations'][0];
    sourceCreatedDate?: string;
    userFullName?: string;
    userIdentity?: string
}) => {

    const {
        profile,
        assignedLocations,
        _id: userId,
        merchantName,
    } = userInfo ?? {};

    const createdByUser = {
        id: userIdentity || userId || null,
        name: userFullName ? userFullName : profile?.lastName && profile?.firstName ? `${profile?.lastName} ${profile?.firstName}` : null,
    };

    const {
        productId,
        quantity,
        unitCost,
        variantId: innervariantId,
        sellingPrice,
        discount,
    } = purchaseItem;

    const creationDate = new Date().toISOString();

    const location = assignedLocations?.[0];

    return {
        _id: `inventory-${uuidv4()}`,
        productId,
        referenceTransactionId: referenceTransactionId || null,
        referenceTransactionNo: referenceTransactionNo || null,
        type: 'inventory',
        transactionType: transactionType || null,
        transactionStatus: transactionStatus || null,
        quantity: quantity || null,
        isVoid: false,
        assignedLocation: assignedLocation || location || null,
        referenceCartId: refCartId || null,
        totalAmount: (quantity * (sellingPrice || 0)) || null,
        sellingPrice: sellingPrice || null,
        discount: discount || null,
        productName: productName || null,
        purchasePrice: unitCost || null,
        merchantName,
        variantId: innervariantId || variantId || null,
        createdAt: sourceCreatedDate || creationDate,
        updatedAt: sourceCreatedDate || creationDate,
        fromLocation: fromLocation || null,
        toLocation: toLocation || null,
        // processed,
        createdBy: createdByUser,
        editedBy: null,
        note: note || null,
        reason: reason || null,
        adjustmentDate: adjustmentDate || null,
        images: (images || []).length > 0 ? images : null,
        externalReferenceNumber: externalReferenceNumber || null,
    };
};

