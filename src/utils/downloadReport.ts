import { FilterBy } from "../models";
import { formatToISOEndOfDayLocal, formatToISOStartOfDayLocal } from "./formatDates";
import { format } from 'date-fns';
import { getEnvs } from "./getEnvs";
import { getSessionInfo } from "./getSessionInfo";


type DownloadReportsParams = {
    filterBy: FilterBy | null,
    endDate?: Date | undefined;
    startDate?: Date | undefined
}


export const downloadSalesReport = async ({ filterBy, endDate, startDate }: DownloadReportsParams) => {

    try {
        const queryParams = new URLSearchParams();

        if (filterBy && (!startDate || !endDate)) {
            queryParams.append('filterBy', filterBy);
        }

        if (startDate && endDate) {
            const formattedStartDate = formatToISOStartOfDayLocal(startDate);
            queryParams.append('startDate', formattedStartDate);

            const formattedEndDate = formatToISOEndOfDayLocal(endDate);
            queryParams.append('endDate', formattedEndDate);

            queryParams.append('filterBy', '');

        }

        const { backendUrl } = getEnvs();

        const { userInfo } = getSessionInfo();

        const pdfResponse = await fetch(`${backendUrl}/report/sales/pdf/download?${queryParams.toString()}`, {
            method: "GET",
            credentials: 'include',
            headers: {
                'Accept': 'application/pdf',
                'Content-Type': 'application/json'
            },
        });

        if (!pdfResponse.ok) throw new Error("Failed to download sales report");


        const response = await pdfResponse.arrayBuffer();
        const blob = new Blob([response], { type: 'application/pdf' });
        const link = document.createElement('a');
        link.href = window.URL.createObjectURL(blob);


        const today = format(new Date(), 'MM dd yyyy');

        let datePart = '';
        if (startDate && endDate) {
            datePart = `${format(startDate, 'MM dd yyyy')}_${format(endDate, 'MM dd yyyy')}`;
        } else {
            datePart = today;
        }

        link.download = `${userInfo?.profile?.lastName} ${userInfo?.profile?.firstName}_daily_sales_report_${datePart}.pdf`;


        link.click()


    } catch (error) {
        console.error("Download failed", error);
    }
};
