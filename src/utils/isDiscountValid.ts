import { Pricing } from '../models';

export const isDiscountValid = (pricing: Pricing): boolean => {

    if (!pricing?.discountValidFrom || !pricing?.discountValidUntil) return false;


    const today = new Date();
    const todayUTC = new Date(Date.UTC(today.getUTCFullYear(), today.getUTCMonth(), today.getUTCDate()));


    const validFrom = pricing.discountValidFrom ? new Date(pricing.discountValidFrom) : null;
    const validUntil = pricing.discountValidUntil ? new Date(pricing.discountValidUntil) : null;

    if (validFrom && todayUTC < validFrom) return false;
    if (validUntil && todayUTC > validUntil) return false;

    return true;
};
