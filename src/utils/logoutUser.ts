import { remoteDB } from '../integrations/pouch/DBConn';
import { getSessionInfo } from './getSessionInfo';
import { updatePinCodeStatus } from './pincode';
import { removeSession } from './storage';

export const logoutUser = async () => {
	const { shoppaUser } = getSessionInfo();
	try {
		await remoteDB('').logOut();
		await updatePinCodeStatus(false);
		removeSession(shoppaUser);
		window.location.href = '/login';
		setTimeout(() => {
			window.location.reload();
		}, 500);
	} catch (err) {
		// await updatePinCodeStatus(false);
		removeSession(shoppaUser);
		window.location.href = '/login';
		setTimeout(() => {
			window.location.reload();
		}, 500);
	}
};
