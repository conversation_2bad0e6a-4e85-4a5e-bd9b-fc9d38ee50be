import { CompanyInfoResponseProps, UserResponseProps } from '../types';
import { getEnvs } from './getEnvs';
import { getSession } from './storage';

export const getSessionInfo = () => {
	const { user, cartId, uri: remoteUrl, backendUrl, companySettings } = getEnvs();

	const userInfo = getSession(user) as UserResponseProps | undefined;

	const locationId = userInfo?.assignedLocations?.[0]?.id;
	const userLocationObject = userInfo?.assignedLocations?.[0];

	const refCartId = (getSession(`${cartId}-${userInfo?.profile?.email}`) as string) ?? '';

	const companyInfo = getSession(companySettings) as CompanyInfoResponseProps | undefined;
	

	return {
		refCartId,
		cartId,
		userInfo,
		user,
		remoteUrl,
		backendUrl,
		locationId,
		userLocationObject,
		shoppaUser: user,
		companyInfo
	};
};