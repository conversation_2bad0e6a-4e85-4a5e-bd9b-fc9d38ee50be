import { ProductItemProps } from '../models';

export const flattenedProductData = (data: ProductItemProps[]) => {
	return (
		(data || [])
			.map((product) => {
				if ((product?.variants || [])?.length > 0) {
					return product?.variants?.map((variant) => {
						if (variant?.isActive === true) {
							return {
								merchantName: product?.merchantName ?? null,
								_id: `${product?._id}:${variant?.id}`,
								productId: product?.productId,
								variantId: variant?.id ?? null,
								productName: product?.productName ?? null,
								description: product?.description ?? null,
								categories: product?.categories ?? null,
								tags: product?.tags ?? null,
								sku: variant?.sku ?? product?.sku ?? null,
								productImage: variant?.productImage ?? product?.productImage ?? null,
								attributes: variant?.attributes ?? null,
								identifiers: variant?.identifiers ?? product?.identifiers ?? null,
								pricing: {
									sellingPrice: variant?.pricing?.sellingPrice ?? product?.pricing?.sellingPrice ?? 0,
									discountAmount: variant?.discount?.discountAmount ?? product?.discount?.discountAmount ?? 0,
									discountPercentage:
										variant?.discount?.discountPercentage ?? product?.discount?.discountPercentage ?? 0,
									discountValidFrom:
										variant?.discount?.discountValidFrom ?? product?.discount?.discountValidFrom ?? null,
									discountValidUntil:
										variant?.discount?.discountValidUntil ?? product?.discount?.discountValidUntil ?? null,
								},
								inventory: {
									openingStock: variant?.inventory?.openingStock ?? product?.inventory?.openingStock ?? 0,
									quantityAvailable:
										variant?.inventory?.quantityAvailable ?? product?.inventory?.quantityAvailable ?? 0,
									reOrderLevel: variant?.inventory?.reOrderLevel ?? product?.inventory?.reOrderLevel ?? null,
									unitCost: variant?.inventory?.unitCost ?? product?.inventory?.unitCost ?? null,
								},
								isVariantProduct: true,
								hasWarrantyCoverage: variant?.hasWarrantyCoverage ?? product?.hasWarrantyCoverage ?? false,
								hasExpiryDate: variant?.hasExpiryDate ?? product?.hasExpiryDate ?? false,
								createdAt: product?.createdAt ?? null,
								updatedAt: product?.updatedAt ?? null,
							};
						}
						return null;
					});
				} else {
					return product?.isActive === true
						? {
								merchantName: product?.merchantName ?? null,
								_id: product?._id ?? null,
								productId: product?.productId,
								productName: product?.productName ?? null,
								description: product?.description ?? null,
								categories: product?.categories ?? null,
								tags: product?.tags ?? null,
								sku: product?.sku ?? null,
								barcode: product?.barcode ?? null,
								productImage: product?.productImage ?? null,
								attributes: null,
								identifiers: product?.identifiers ?? null,
								pricing: {
									sellingPrice: product?.pricing?.sellingPrice ?? 0,
									discountAmount: product?.discount?.discountAmount ?? 0,
									discountPercentage: product?.discount?.discountPercentage ?? 0,
									discountValidFrom: product?.discount?.discountValidFrom ?? null,
									discountValidUntil: product?.discount?.discountValidUntil ?? null,
								},
								inventory: {
									openingStock: product?.inventory?.openingStock ?? 0,
									quantityAvailable: product?.inventory?.quantityAvailable ?? 0,
									reOrderLevel: product?.inventory?.reOrderLevel ?? null,
									unitCost: product?.inventory?.unitCost ?? null,
								},
								isVariantProduct: false,
								hasWarrantyCoverage: product?.hasWarrantyCoverage ?? false,
								hasExpiryDate: product?.hasExpiryDate ?? false,
								createdAt: product?.createdAt ?? null,
								updatedAt: product?.updatedAt ?? null,
							}
						: null;
				}
			})
			.flat()
			.filter(Boolean) ?? []
	);
};
