export const checkPinValidity = (arg: any) => Object.values(arg).every(Boolean);

export const convertObjectToString = (v: any) => Object.values(v).join('');

export const checkIsEqualPin = (pin1: any, pin2: any) => {
	return convertObjectToString(pin1) === convertObjectToString(pin2);
};

export const formatAmount = (num: number | string): string => {
	const numberValue = typeof num === 'string' ? parseFloat(num) : num;

	if (isNaN(numberValue)) return '0.00';

	return numberValue.toLocaleString('en-US', {
		minimumFractionDigits: 2,
		maximumFractionDigits: 2,
	});
};

export const convertWordUpperCase = (word: string) => {
	return word
		.split('')
		.map((v: string, i) => (i === 0 ? v.toUpperCase() : v))
		.join('');
};
