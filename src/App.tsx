import React, { useEffect } from 'react';

import './App.scss';

import { Routes, Route, Navigate, useLocation } from 'react-router-dom';

import plugins from './integrations/pouch/plugins';

import { configEnc } from './utils/enc';

import Private from './authentication/PrivateRoute';
import Public from './authentication/PublicRoute';

import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

import { LazyLogin, LazyReset, LazySalesTerminal, LazyPayment } from './pages-chunks';

import NotFound from './components/UtilComponents/NotFound';
import FallBack from './components/UtilComponents/FallBack';
import { handleShowPinCodeScreen } from './store/slices/authSlice';
import PincodeAuth from './components/auth/pincode';
import { useAppDispatch, useAppSelector } from './store/storeConfig';
import { trackPinCodeStatus, updatePinCodeStatus } from './utils/pincode';
import { useIdleTimer } from 'react-idle-timer';
import { useRegisterReplicationHooks } from './hooks/useRegisterReplicationHooks';
import SyncInfo from './components/sync-info';
import useTrackPincodeEnabled from './hooks/useTrackPincodeEnabled';
import SalesReportPage from './pages/SalesReport';

plugins();

configEnc();

const IDLE_TIMEOUT = 5_000_000;

function App() {
	useRegisterReplicationHooks();
	useTrackPincodeEnabled();

	const location = useLocation();

	useIdleTimer({
		timeout: IDLE_TIMEOUT,
		promptBeforeIdle: Math.floor(IDLE_TIMEOUT / 2),
		events: ['click', 'mousedown', 'mouseenter', 'keydown'],
		debounce: 200,
		onPrompt: async () => {
			if (location.pathname === '/terminal' || location.pathname === '/payment') {
				await updatePinCodeStatus(false);
				dispatch(handleShowPinCodeScreen(true));
				return;
			} else {
				dispatch(handleShowPinCodeScreen(false));
			}
		},
	});

	const { isReplicatingProduct } = useAppSelector((state) => state.productSlice);

	const dispatch = useAppDispatch();

	const { showPincodeScreen } = useAppSelector((state) => state.authSlice);

	useEffect(() => {
		if (location.pathname === '/terminal' || location.pathname === '/payment') {
			trackPinCodeStatus();
		} else {
			dispatch(handleShowPinCodeScreen(false));
		}
	}, [location]);

	return (
		<>
			{isReplicatingProduct && <SyncInfo />}

			<ToastContainer
				autoClose={5000}
				hideProgressBar={false}
				newestOnTop={false}
				closeOnClick
				rtl={false}
				pauseOnFocusLoss
				draggable
				pauseOnHover
			/>
			{showPincodeScreen && <PincodeAuth />}
			<React.Suspense fallback={<FallBack />}>
				<Routes>
					<Route path="/" element={<Navigate to="/login" />} />
					<Route
						path="/passcode"
						element={
							<Private>
								<LazyReset />
							</Private>
						}
					/>

					<Route
						path="/terminal"
						element={
							<Private>
								<LazySalesTerminal />
							</Private>
						}
					/>
					<Route
						path="/payment"
						element={
							<Private>
								<LazyPayment />
							</Private>
						}
					/>
					<Route
						path="/sales-reports"
						element={
							<Private>
								<SalesReportPage />
							</Private>
						}
					/>
					<Route
						path="/login"
						element={
							<Public>
								<LazyLogin />
							</Public>
						}
					/>
					<Route path="*" element={<NotFound />} />
				</Routes>
			</React.Suspense>
		</>
	);
}

export default App;
