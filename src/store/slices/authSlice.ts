import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface IAuthSliceDataProps {
	authenticated: boolean;
	showPincodeScreen: boolean;
}

const authSliceData: IAuthSliceDataProps = {
	authenticated: false,
	showPincodeScreen: false,
};

const authSlice = createSlice({
	name: 'authSlice',
	initialState: authSliceData,
	reducers: {
		handleAuthentication: (state, { payload }: PayloadAction<boolean>) => {
			state.authenticated = payload;
		},
		handleShowPinCodeScreen: (state, { payload }: PayloadAction<boolean>) => {
			state.showPincodeScreen = payload;
		},
	},
});

export const { handleAuthentication, handleShowPinCodeScreen } = authSlice.actions;

export default authSlice.reducer;
