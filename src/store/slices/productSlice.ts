import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface IAuthSliceDataProps {
  isProductReady: boolean;
  category: null | string;
  isReplicatingProduct: boolean;
  currentProductId: string | null;
  isScanningBarcode: boolean
}

const productSliceData: IAuthSliceDataProps = {
  isProductReady: false,
  category: null,
  isReplicatingProduct: false,
  currentProductId: null,
  isScanningBarcode: false
};

const productSlice = createSlice({
  name: "authSlice",
  initialState: productSliceData,
  reducers: {
    handleBarCodeScan: (
      state, { payload }: PayloadAction<boolean>
    ) => {
      state.isScanningBarcode = payload;
    },
    handleProductReady: (state, { payload }: PayloadAction<boolean>) => {
      state.isProductReady = payload;
    },
    handleReplicateProduct: (state, { payload }: PayloadAction<boolean>) => {
      state.isReplicatingProduct = payload;
    },
    handleCategory: (state, { payload }: PayloadAction<string>) => {
      state.category = payload;
    },
    handleCurrentProductId: (state, { payload }: PayloadAction<string | null>) => {
      state.currentProductId = payload
    }

  },
});

export const { handleProductReady, handleCategory, handleReplicateProduct, handleCurrentProductId, handleBarCodeScan } =
  productSlice.actions;

export default productSlice.reducer;
