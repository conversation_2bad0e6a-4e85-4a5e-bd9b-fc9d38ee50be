export const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

// Function to get days based on selected month
export const getDaysForMonth = (month: string): string[] => {
	const daysInMonth: { [key: string]: number } = {
		Jan: 31,
		Feb: 29,
		Mar: 31,
		Apr: 30,
		May: 31,
		Jun: 30,
		Jul: 31,
		Aug: 31,
		Sep: 30,
		Oct: 31,
		Nov: 30,
		Dec: 31,
	};

	const numDays = daysInMonth[month] || 31;
	const days: string[] = [];

	for (let i = 1; i <= numDays; i++) {
		days.push(i.toString().padStart(2, '0'));
	}

	return days;
};
