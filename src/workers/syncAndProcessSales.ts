/* eslint-disable no-restricted-globals */
import { remoteDB } from '../integrations/pouch/DBConn';
import plugins from '../integrations/pouch/plugins';
import { EDDocPath, SalesProps, SalesStatus } from '../models';
import { getSessionInfo } from '../utils/getSessionInfo';
import PouchDB from 'pouchdb';
import { UserResponseProps } from '../types';
import { handleCouchError } from '../utils/handleCouchError';

plugins();

interface MessageEventData {
	userInfo: UserResponseProps;
	salesPayload?: SalesProps;
	status?: 'pause' | 'resume' | 'process-now';
}

const salesDB = (userInfo: UserResponseProps) => {

	return new PouchDB(
		`${userInfo?.merchantName}-${userInfo?.assignedLocations?.[0]?.id}-sales`,
		{
			skip_setup: true,
			revs_limit: 1,
			auto_compaction: true,
		}
	);
};

const inventoryDB = (userInfo: UserResponseProps) => {

	return new PouchDB(
		`${userInfo?.merchantName}-${userInfo?.assignedLocations?.[0]?.id}-inventory`,
		{
			skip_setup: true,
			revs_limit: 1,
			auto_compaction: true,
		}
	);
	// return new PouchDB(
	// 	`${userInfo?.merchantName}-${userInfo?.assignedLocations?.[0]?.id}-${userInfo?.profile?.email}-sales`,
	// 	{
	// 		skip_setup: true,
	// 		revs_limit: 1,
	// 		auto_compaction: true,
	// 	}
	// );
};


const getPendingAndProcessingSales = async (userInfo: UserResponseProps) => {
	const res = await salesDB(userInfo).find({
		selector: {
			$or: [{ status: SalesStatus.processing }, { status: SalesStatus.pending }],
		},
		limit: 50,
	});

	return res.docs as unknown as SalesProps[];
};

let timerLimit = 1;
const maxLimit = 20000;
const minLimit = 5000;
let isProcessingSales = false;

self.onmessage = async function (event: MessageEvent<MessageEventData>) {
	console.log('this has been fired man, event worker');

	const { userInfo } = event.data;


	if (!userInfo || !userInfo?._id) return;

	const { backendUrl } = getSessionInfo();
	let timeoutRef: NodeJS.Timeout | null = null;

	// let replicationRef: PouchDB.Replication.Sync<{}> | null = null;

	let replicationRef: any = null;

	const sendSalesForProcessing = async (salesData: SalesProps) => {
		try {
			const response = await fetch(`${backendUrl}/product/process-sales`, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({ sale: salesData }),
				credentials: 'include',
			});

			if (!response.ok) {
				throw new Error(`HTTP error! status: ${response.status}`);
			}

			const data = await response.json();
			return data;
		} catch (error) {
			console.error('An error occurred during sendSalesForProcessing:', error);
			throw new Error('something went wrong');
		}
	};

	const processSalesWhenOnline = async () => {
		clearTimeout(timeoutRef!);

		if (!navigator.onLine) {
			timerLimit *= 2;
			initiateSalesProcessing(timerLimit);
			return;
		}

		if (isProcessingSales) {
			timerLimit += 2000;

			initiateSalesProcessing(timerLimit);

			return;
		}

		try {
			isProcessingSales = true;

			const salesTransactionData = await getPendingAndProcessingSales(userInfo);

			if (salesTransactionData.length <= 0) {
				timerLimit *= 2;
				// throw 'no transactions available';
				// initiateSalesProcessing(timerLimit);
				return;
			}

			await Promise.all(salesTransactionData.map(sendSalesForProcessing));

			timerLimit = minLimit;
		} catch (error) {
			console.error('Error processing sales:', error);
			timerLimit = Math.min(timerLimit * 2, maxLimit);
		} finally {
			isProcessingSales = false;
			initiateSalesProcessing(timerLimit * 2);
		}
	};

	const replicateAndProcessWhenOnline = async () => {
		try {
			//note: sync needs to be running as well at any reasonable level
			await inventoryDB(userInfo).replicate.to(remoteDB(userInfo.merchantName), { retry: true });
			await salesDB(userInfo).replicate.to(remoteDB(userInfo.merchantName), { retry: true });



			// await salesDB(userInfo)
			// 	.replicate.from(remoteDB(userInfo.merchantName), {
			// 		retry: false,
			// 		filter: EDDocPath.sales,
			// 		live: undefined,
			// 		query_params: {
			// 			type: 'sales',
			// 			locationId: userInfo?.assignedLocations?.[0]?.id,
			// 			cashierId: userInfo?._id,
			// 		},
			// 	})
			// 	.on('denied', (info: any) => {
			// 		console.log(info, 'worker info active');
			// 	})
			// 	.on('complete', async () => {
			// 		clearTimeout(timeoutRef!);

			// 		await processSalesWhenOnline();
			// 		// salesDB(userInfo).destroy();
			// 		// salesDB(userInfo);
			// 	})
			// 	.on('error', (info) => {
			// 		const errorInfo = info as { [key: string]: any };
			// 		handleCouchError(errorInfo);
			// 		throw new Error(`${info} error from worker sync`);
			// 	});
		} catch (error) {
			console.error('Error in replication:', error);
			timerLimit = Math.min(timerLimit * 2, maxLimit);
			initiateSalesProcessing(timerLimit);
		}
	};



	const initiateSalesProcessing = async (interval: number) => {
		await replicateAndProcessWhenOnline();

		// timeoutRef = setTimeout(async () => {
		// 	await replicateAndProcessWhenOnline();
		// }, interval);
	};

	await initiateSalesProcessing(timerLimit);

	self.close = () => {
		if (timeoutRef) {
			clearTimeout(timeoutRef);
		}

		if (replicationRef) {
			replicationRef.cancel();
		}
	};
};

export { };
