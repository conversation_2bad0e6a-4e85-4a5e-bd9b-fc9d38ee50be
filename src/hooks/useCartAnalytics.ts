import { CartProps, EVat } from '../models';
import bigDecimal from 'js-big-decimal';
import { getSessionInfo } from '../utils/getSessionInfo';

export const useCartAnalytics = (cartItems: CartProps['products'] = []) => {
  const cartLength = cartItems.length;

  const cartSum = cartItems.reduce((acc, item) => {
    const discount = item?.isDiscountValid ? new bigDecimal(item.discount ?? 0) : new bigDecimal(0);
    const sellingPrice = new bigDecimal(item.sellingPrice ?? 0);
    const quantity = new bigDecimal(item.quantity ?? 0);

    const discountedPrice = sellingPrice.subtract(discount);
    const total = discountedPrice.multiply(quantity);

    return acc.add(total);
  }, new bigDecimal(0));

  const cartSumWithoutDiscount = cartItems.reduce((acc, item) => {
    const sellingPrice = new bigDecimal(item.sellingPrice ?? 0);
    const quantity = new bigDecimal(item.quantity ?? 0);

    const total = sellingPrice.multiply(quantity);
    return acc.add(total);
  }, new bigDecimal(0));

  const { companyInfo } = getSessionInfo();

  const totalSavings = cartSumWithoutDiscount.subtract(cartSum);
  const vatAmount = cartSum.multiply(new bigDecimal(+(companyInfo?.tax ?? 0) / 100));
  const payableAmount = cartSum.add(vatAmount);

  return {
    totalSavings: totalSavings.round(2).getValue(),
    payableAmount: payableAmount.round(2).getValue(),
    cartLength,
    cartSum: cartSum.getValue(),
    vatAmount: vatAmount.round(2).getValue(),
    cartSumWithoutDiscount: cartSumWithoutDiscount.round(2).getValue(),
  };
};
