import { useEffect, useState } from 'react';

type UseIdeleTimeoutProps = {
	events: string[];
	timeout: number;
	onIdle: () => void;
};

export const useCustomIdleTimer = ({ onIdle, timeout = 100000 }: UseIdeleTimeoutProps) => {
	const [isIdle, setIsIdle] = useState(false);

	useEffect(() => {
		let timer: NodeJS.Timeout | null;

		const handleUserActivity = () => {
			if (isIdle) {
				setIsIdle(false);
			}
			timer && clearTimeout(timer);
			timer = setTimeout(() => {
				setIsIdle(true);
				onIdle();
			}, timeout);
		};

		window.addEventListener('mousemove', handleUserActivity);
		window.addEventListener('keydown', handleUserActivity);

		timer = setTimeout(() => {
			setIsIdle(true);
			onIdle();
		}, timeout);

		return () => {
			timer && clearTimeout(timer);
			window.removeEventListener('mousemove', handleUserActivity);
			window.removeEventListener('keydown', handleUserActivity);
		};
	}, [timeout, isIdle, onIdle]);

	return isIdle;
};

// export default useIdleTimer;

// export const useIdleTimeout = (
//     {
//         events,
//         idleTime = 40000,
//         handleTimeOutReached
//     }
//         : UseIdeleTimeoutProps
// ) => {

//     const timeOutRef = React.useRef<NodeJS.Timeout | null>(null)

//     const handleClearTimeOut = () => {
//         if (timeOutRef.current) {
//             clearTimeout(timeOutRef.current);
//             scheduleTimeOut(idleTime * 2);
//         }
//     }

//     const scheduleTimeOut = (delay?: number) => {
//         timeOutRef.current = setTimeout(() => {
//             handleTimeOutReached();
//         }, delay || idleTime);
//     }

//     React.useEffect(() => {

//         scheduleTimeOut();

//         events.forEach((e) => {
//             window.addEventListener(e, handleClearTimeOut)
//         });

//         return () => {
//             events.forEach((e) => {
//                 window.removeEventListener(e, handleClearTimeOut)
//             });
//         }

//     }, [])

// }
