import { useEffect, RefObject } from "react";

type ClickOutsideProps = {
    ref: RefObject<HTMLElement> | null;
    callback: () => void;
};

export const useClickOutside = ({ ref, callback }: ClickOutsideProps) => {
    useEffect(() => {
        function handleClickOutside(event: MouseEvent) {
            if (!ref?.current) return;

            if (ref.current && !ref.current?.contains(event.target as Node)) {
                callback();
            }
        }

        document.addEventListener("mousedown", handleClickOutside);

        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, [ref, callback]);
};
