import React from 'react';

import { useAuthenticate } from './useAuthenticate';

import { IFilter } from '../models/index';

import { genericMerchantDB, remoteDB } from '../integrations/pouch/DBConn';
import { getSessionInfo } from '../utils/getSessionInfo';
import { handleCouchError } from '../utils/handleCouchError';
interface IReplicator {
	localDBName: string;
	filterInitial: IFilter;
	filterSubsequent: IFilter;
	filterFunc?: (v: { [key: string]: unknown }) => { [key: string]: unknown };
	handleReplicationStatus: (v: boolean) => void;
	updateOnChange: (arg: Record<string, string>) => Promise<void>;
}

export const useReplicatorFrom = ({
	localDBName,
	filterInitial,
	filterSubsequent,
	filterFunc,
	handleReplicationStatus,
	updateOnChange,
}: IReplicator) => {
	const [isReplicating, setIsReplicating] = React.useState(false);

	const replicateRef = React.useRef<any>(null);

	const liveReplicationRef = React.useRef<any>(null);

	const isAuthenticated = useAuthenticate();

	const { userInfo } = getSessionInfo();

	React.useEffect(() => {
		if (!isAuthenticated) {
			return;
		}

		handleReplicationStatus(true);
		setIsReplicating(true);

		replicateRef.current = genericMerchantDB(localDBName)
			.replicate.from(remoteDB(userInfo?.merchantName as string), {
				...(filterFunc ? filterFunc({ ...filterInitial, live: undefined, retry: true }) : { ...filterInitial, live: undefined, retry: true }),
				timeout: 3000,
			})
			.on('complete', (info) => {
				if (info.status === 'complete') {
					handleReplicationStatus(false);
					setIsReplicating(false);

					liveReplicationRef.current = genericMerchantDB(localDBName)
						.replicate.from(
							remoteDB(userInfo?.merchantName as string),
							filterFunc ? filterFunc({ ...filterSubsequent, live: true, retry: true }) : {
								...filterSubsequent, live: true, retry: true
							}
						)
						.on('error', (err) => {
							handleCouchError(err);
						})
						.on('change', async (info: PouchDB.Replication.ReplicationResult<{}>) => {
							//TODO: handle change callbacks explicit
							await updateOnChange(info?.docs?.map((doc) => doc) as any);
						});
				}
			})
			.on('error', (error) => {
				console.log(error);
				handleCouchError(error)
			})
			.on('change', async (info: PouchDB.Replication.ReplicationResult<{}>) => {
				await updateOnChange(info.docs?.map((doc) => doc) as any);
			})
			.on('denied', () => { });

		return () => {
			replicateRef.current && replicateRef.current?.cancel();

			liveReplicationRef.current && liveReplicationRef?.current.cancel();
		};
	}, []);

	return { isReplicating };
};
