import { renderHook } from '@testing-library/react';

import { useCartAnalytics } from '../useCartAnalytics';

const analytic_data = [
	{
		sellingPrice: 10,
		discount: 1,
		quantity: 1,
		isDiscountValid: true,
	},
	{
		sellingPrice: 10,
		discount: 1,
		quantity: 1,
		isDiscountValid: true,
	},
];

//todo: update test payload
describe('===cart analytics===', () => {
	it('should invoke without crashing', () => {
		renderHook(() => useCartAnalytics(analytic_data as unknown as any));
	});

	it('should return exact numbers of cart length', () => {
		const { result } = renderHook(() => useCartAnalytics(analytic_data as any));

		expect(result.current.cartLength).toBe(2);
	});

	it('should return correct summation of cart items without discount', () => {
		const { result } = renderHook(() => useCartAnalytics(analytic_data as any));
		expect(result.current.cartSumWithoutDiscount).toBe('20.00');
	});

	it('should return correct summation of cart items with discount', () => {
		const { result } = renderHook(() => useCartAnalytics(analytic_data as any));
		expect(result.current.cartSum).toBe('18');
	});

	it('should return correct  analysis of vat', () => {
		const { result } = renderHook(() => useCartAnalytics(analytic_data as any));
		expect(result.current.vatAmount).toBe('0.00');
	});

	it('should return correct  analysis of payable amount', () => {
		const { result } = renderHook(() => useCartAnalytics(analytic_data as any));
		expect(result.current.payableAmount).toBe('18.00');
	});

	it('should return correct  analysis of total savings', () => {
		const { result } = renderHook(() => useCartAnalytics(analytic_data as any));
		expect(result.current.totalSavings).toBe('2.00');
	});
});
