import React from "react";



export const useWindowHeight = () => {
  const [height, setHeight] = React.useState(() =>
    document.getElementById('product-wrapper')?.clientHeight || 0
  );

  const handleHeight = React.useCallback(() => {
    const wrapper = document.getElementById('product-wrapper');
    if (wrapper) {
      setHeight(wrapper.clientHeight);
    }
  }, []);

  React.useEffect(() => {
    handleHeight();

    window.addEventListener("resize", handleHeight);
    return () => window.removeEventListener("resize", handleHeight);
  }, []); // Only run on mount/unmount

  return { height };
};


const useWindowWidth = () => {
  const [width, setWidth] = React.useState(window.innerWidth);

  const handleWidth = () => {
    setWidth(window.innerWidth);
  };

  React.useEffect(() => {
    handleWidth();
    window.addEventListener("resize", handleWidth);
    return () => window.removeEventListener("resize", handleWidth);
  }, [width]);

  return { width };
};

export default useWindowWidth;
