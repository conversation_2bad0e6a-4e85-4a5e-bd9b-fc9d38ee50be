import React from 'react';
import { useCart } from './useCart';
import { pullProducts } from '../integrations/react_query/queryFn';

type useBarCodeScanProps = {
	ref: React.MutableRefObject<HTMLInputElement | null>;
	setValue: (arg: string) => void;
	debounceValue: string;
};

const INTERVAL = 1000;

const SCAN_THRESHOLD = 100;

const TARGET_NODE = 'input';
const ENTER_EVENT = 'Enter';
const SHIFT_EVENT = 'Shift';
const KEY_PRESS_EVENT = 'keypress';

export const useBarCodeScan = ({ ref, setValue }: useBarCodeScanProps) => {
	const timeoutRef = React.useRef<NodeJS.Timeout | null>(null);
	const barCodeInputRef = React.useRef<string>('');
	let lastSearch = '';

	let lastInputTime = 0;

	const scanningStatus = [];

	const { handleAddToCart } = useCart();

	const addScanItemToCart = async (barcode: string | undefined) => {
		if (!barcode) return;

		const products = await pullProducts();

		const productItem = products?.find((flattenedProduct) => {
			return flattenedProduct?.barcode?.toString() === barcode;
		});

		if (!productItem) return;

		handleAddToCart({ ...productItem, quantity: 1 });
	};

	const handleBarCodeInput = async (event: KeyboardEvent) => {
		const currentTime = new Date().getTime();

		const timeSinceLastInput = currentTime - lastInputTime;

		if (timeSinceLastInput < SCAN_THRESHOLD) {
			scanningStatus.push(true);

			lastInputTime = currentTime;
		}

		const targetSource = event.target as { [key: string]: any };

		if (
			(targetSource?.localName?.toLowerCase() === TARGET_NODE ||
				targetSource?.nodeName?.toLowerCase() === TARGET_NODE) &&
			event.code !== ENTER_EVENT
		) {
			return;
		}

		if (
			(targetSource?.localName?.toLowerCase() === TARGET_NODE ||
				targetSource?.nodeName?.toLowerCase() === TARGET_NODE) &&
			event.code === ENTER_EVENT
		) {
			if (targetSource?.value) {
				addScanItemToCart(targetSource.value);

				setValue('');

				timeoutRef.current = setTimeout(() => {
					barCodeInputRef.current = '';
				}, INTERVAL);
			}

			return;
		}

		if (event.code === ENTER_EVENT) {
			if (barCodeInputRef.current) {
				addScanItemToCart(barCodeInputRef.current);

				if (lastSearch === barCodeInputRef.current) return;
				setValue(barCodeInputRef.current);
				barCodeInputRef.current = '';
				lastSearch = barCodeInputRef.current;
			}
			return;
		}

		if (event.key !== SHIFT_EVENT) {
			barCodeInputRef.current += event.key;
		}

		if (timeoutRef.current) {
			clearTimeout(timeoutRef.current);
		}

		timeoutRef.current = setTimeout(() => {
			barCodeInputRef.current = '';
		}, INTERVAL);
	};

	const focusInputRef = () => {
		document.body.focus();
		if (ref.current) {
			// ref.current.focus();
			document.body.focus();
		}
	};

	React.useEffect(() => {
		focusInputRef();

		document.addEventListener(KEY_PRESS_EVENT, handleBarCodeInput);

		return () => {
			document.removeEventListener(KEY_PRESS_EVENT, handleBarCodeInput);

			if (timeoutRef.current) {
				clearTimeout(timeoutRef.current);
			}
		};
	}, []);
	return barCodeInputRef.current;
};
