import { useState } from "react";

type BookmarkPagination = {
    currentBookmark: string | null;
    nextPageBookmark?: string;
    previousPageBookmark?: string;
}

export const useBookmarkPagination = () => {
    const [bookmarkStack, setBookmarkStack] = useState<string[]>([]);
    const [currentBookmark, setCurrentBookmark] = useState<string | null>(null);

    const handleNextPage = (nextPageBookmark?: string) => {
        if (nextPageBookmark) {
            setBookmarkStack((prev) => [...prev, currentBookmark ?? ""]);
            setCurrentBookmark(nextPageBookmark);
        }
    };

    const handlePreviousPage = () => {
        if (bookmarkStack.length > 0) {
            const newStack = [...bookmarkStack];
            const previousBookmark = newStack.pop();

            setBookmarkStack(newStack);
            setCurrentBookmark(previousBookmark || null);
        }
    };

    return {
        currentBookmark,
        handleNextPage,
        handlePreviousPage,
    };
};

export default useBookmarkPagination;
