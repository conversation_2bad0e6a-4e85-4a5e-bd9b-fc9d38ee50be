import { getSessionInfo } from './../utils/getSessionInfo';
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { IFilter, SalesStatus } from '../models/index';
import { remoteDB, salesDB } from '../integrations/pouch/DBConn';
import { axiosInstanceShoppaBackend } from '../integrations/axios.config';
import { getAllSales } from '../integrations/react_query/queryFn';
import { handleCouchError } from '../utils/handleCouchError';

interface IReplicator {
	localDBName: string;
	filter: IFilter;
	filterFunc: (arg: any) => any;
}

export const useSyncSales = ({ filter, filterFunc }: IReplicator) => {
	const [isReplicating, setIsReplicating] = React.useState(false);
	const [retryCount, setRetryCount] = React.useState(0);

	const replicateRef = React.useRef<any>(null);

	const { userInfo } = getSessionInfo();

	const [isProcessingSales, setIsProcessingSales] = React.useState(false);

	const retrySync = async (salesData: any[]) => {
		try {
			await axiosInstanceShoppaBackend.post('/product/process-sales', { sale: salesData }, { withCredentials: true });
			setIsReplicating(true);
			setRetryCount(0);
		} catch (error) {
			if (isServerRelatedError(error) && retryCount < MAX_RETRIES) {
				setRetryCount((prevCount) => prevCount + 1);
				setTimeout(() => retrySync(salesData), RETRY_DELAY);
			} else {
				console.error('Max retry attempts reached or non-server error. Sync failed.');
				setIsReplicating(true);
			}
		}
	};

	const isServerRelatedError = (error: any) => {
		return !error.response;
	};

	//TODO: get all sales transactions items, then check for those that are yet to be processed, then send them for processing or pending, pending has been logged but not worked on.
	//There also need to be background jobs running once in a while in order to capture some edge case scenarios

	const onChangeHandler = async (info: any) => {
		const docs = info?.change?.docs || [];
		const newFiltered = docs.filter((d: any) => {
			return (
				(d?.status === SalesStatus.processing || d?.status === SalesStatus.pending) && d?.cashier?.id === userInfo?._id
			);
		});

		// if (newFiltered.length > 0) {
		//     for await (const doc of newFiltered) {
		//         retrySync(doc);
		//     }
		// }
	};

	const replicateSalesRef = React.useRef<any>(null);

	const MAX_RETRIES = 3;
	const RETRY_DELAY = 5000;
	React.useEffect(() => {
		//TODO: authenticate

		//offline purpose
		replicateSalesRef.current = salesDB()
			.replicate.to(remoteDB(userInfo?.merchantName), { retry: true })
			.on('complete', async (info) => {
				if (info.status === 'complete') {
					try {
						setIsProcessingSales(true);

						const salesDataResponse: any[] = await getAllSales();

						const filteredSales = salesDataResponse.filter((salesData) => {
							return (
								(salesData?.status === SalesStatus.processing || salesData?.status === SalesStatus.pending) &&
								salesData?.cashier?.id === userInfo?._id
							);
						});

						if (filteredSales?.length > 0) {
							const salesToProcess = filteredSales.map((filteredSale) => retrySync(filteredSale));
							await Promise.all(salesToProcess);

							setIsProcessingSales(false);
						}
					} catch (err) {
						console.log(err, 'from processing sales');
					} finally {
						setIsProcessingSales(false);
					}
				}
			})
			.on('error', (err: any) => {
				//TODO: do something
				handleCouchError(err);
			})
			.on('change', (info) => {
				//TODO: do something
			});

		replicateRef.current = salesDB()
			.sync(remoteDB(userInfo?.merchantName), filterFunc({ ...filter, live: true }))
			.on('complete', (info) => {
				setIsReplicating(true);
			})
			.on('error', () => {
				setIsReplicating(true);
			})
			.on('change', onChangeHandler);

		return () => {
			if (replicateRef.current) {
				replicateRef.current.cancel();
			}

			if (replicateSalesRef.current) {
				replicateSalesRef.current.cancel();
			}
		};
	}, []);

	return { isReplicating, isProcessingSales };
};
