import { useQueryClient } from 'react-query';
import { useAppDispatch } from '../store/storeConfig';
import { useReplicatorFrom } from './useReplicatorFrom';
import {
	cashiersFilter,
	companySettingsFilter,
	customersFilter,
	optsCashiers,
	optsCustomer,
	optsProducts,
	optsSales,
	productFilterInitial,
	productFilterSubsequent,
	salesFilter,
	paymentTypesFilter,
	optsPaymentTypes,
} from '../integrations/pouch/opts';
import { ELocalDBNames, EQuerykeys } from '../models';
import { handleReplicateProduct } from '../store/slices/productSlice';
import { invalidators } from '../integrations/react_query/invalidators';
import { useReplicatorTo } from './useReplicateTo';
import { supportWebWorker, useReplicateAndProcessSales } from './useReplicateAndProcessSalesTo';
import React from 'react';
import { getSessionInfo } from '../utils/getSessionInfo';
import { useOnlineStatus } from './useOnline';
import { useAuthenticate } from './useAuthenticate';
import { saveSession } from '../utils/storage';
import { getEnvs } from '../utils/getEnvs';

export const useRegisterReplicationHooks = () => {
	const dispatch = useAppDispatch();

	const queryClient = useQueryClient();

	const { userInfo } = getSessionInfo();

	const isOnline = useOnlineStatus();

	const worker: Worker = new Worker(new URL('.././workers/syncAndProcessSales.ts', import.meta.url));

	const isAuthenticated = useAuthenticate();

	React.useEffect(() => {
		if (!isAuthenticated) {
			return;
		}

		if (!supportWebWorker()) return;
		worker.postMessage({ userInfo });

		worker.onmessage = function (arg) {
			console.log(arg);
		};

		return () => {
			worker.terminate();
		};
	}, [isOnline, worker]);

	//todo: analyse the registered sales processor hook

	useReplicatorFrom({
		localDBName: `${userInfo?.profile?.email}-${ELocalDBNames.sales}`,
		filterInitial: salesFilter(),
		filterSubsequent: salesFilter(),
		filterFunc: optsSales,
		handleReplicationStatus: (v: boolean) => () => {}, //change this later,
		updateOnChange: invalidators(queryClient)[EQuerykeys.sales],
	});

	useReplicatorFrom({
		localDBName: ELocalDBNames.product,
		filterInitial: productFilterInitial(),
		filterSubsequent: productFilterSubsequent(),
		filterFunc: optsProducts,
		handleReplicationStatus: (v: boolean) => dispatch(handleReplicateProduct(v)),
		updateOnChange: invalidators(queryClient)[EQuerykeys.product],
	});

	useReplicatorFrom({
		localDBName: ELocalDBNames.cashiers,
		filterInitial: cashiersFilter(),
		filterSubsequent: cashiersFilter(),
		filterFunc: optsCashiers,
		handleReplicationStatus: (v: boolean) => () => {}, //change this later,
		updateOnChange: invalidators(queryClient)[EQuerykeys.cashier],
	});

	useReplicatorFrom({
		localDBName: ELocalDBNames.payment_types,
		filterInitial: paymentTypesFilter(),
		filterSubsequent: paymentTypesFilter(),
		filterFunc: optsPaymentTypes,
		handleReplicationStatus: (v: boolean) => () => {}, //change this later,
		updateOnChange: invalidators(queryClient)[EQuerykeys.payment_types],
	});

	useReplicatorFrom({
		localDBName: ELocalDBNames.company_settings,
		filterInitial: companySettingsFilter(),
		filterSubsequent: companySettingsFilter(),
		handleReplicationStatus: (v: boolean) => () => {}, //change this later,
		updateOnChange: async (doc) => {
			const maybeSettingDocs = (doc?.[0] ?? {}) as any;

			const { _revisions, _rev, ...companySettingsDoc } = maybeSettingDocs ?? ({} as any);

			const { companySettings } = getEnvs();

			if (companySettingsDoc) {
				saveSession(companySettings, companySettingsDoc ?? {});
			}
		},
	});

	useReplicatorFrom({
		localDBName: ELocalDBNames.customer,
		filterInitial: customersFilter(),
		filterSubsequent: customersFilter(),
		filterFunc: optsCustomer,
		handleReplicationStatus: (v: boolean) => () => {}, //change this later,
		updateOnChange: invalidators(queryClient)[EQuerykeys.customer],
	});

	useReplicatorTo({
		localDBName: ELocalDBNames.customer,
		handleReplicationStatus: (v: boolean) => () => {}, //change this later,
		updateOnChange: invalidators(queryClient)[EQuerykeys.customer],
	});

	// useSyncSales({
	//     localDBName: ELocalDBNames.sales,
	//     filter: salesFilter(),
	//     filterFunc: optsSales,
	// });

	//TODO: uncomment this later
	useReplicateAndProcessSales();

	// useSync({
	//   localDBName: ELocalDBNames.customer,
	//   filter: customersFilter(),
	//   filterFunc: optsCustomer,
	//   handleSyncStatus: () => {},
	// });

	// useDBChanges({
	//   localDBName: ELocalDBNames.product,
	//   qClient: productChangesqClient,
	//   shouldBeginChange: isProductReady,
	// });

	// useDBChanges({
	//   localDBName: ELocalDBNames.sales,
	//   qClient: salesChangesqClient,
	//   shouldBeginChange: true,
	// });

	//const [isReplicatingCart] = useReplicator({ localDBName: "carts" });
	//const [isReplicatingTransaction] = useReplicator({
	//  localDBName: "transactions",
	//});
};
