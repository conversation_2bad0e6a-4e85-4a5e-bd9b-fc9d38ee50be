import { useSyncExternalStore } from "react";

function subscribe(callback: () => void) {
    window.addEventListener('online', callback);
    window.addEventListener('offline', callback);
    return () => {
        window.removeEventListener('online', callback);
        window.removeEventListener('offline', callback);
    };
}

export function getOnlineSnapshot() {
    return navigator.onLine;
}

export const useOnlineStatus = () => {
    const isOnline = useSyncExternalStore(subscribe, getOnlineSnapshot);
    return isOnline;
}