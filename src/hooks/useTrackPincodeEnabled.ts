import { useLocation } from 'react-router-dom';
import { getSessionInfo } from '../utils/getSessionInfo';
import { useEffect } from 'react';

const useTrackPincodeEnabled = () => {
	const { userInfo } = getSessionInfo();

	const location = useLocation();

	useEffect(() => {
		if (!userInfo?.flags?.pinCodeEnabled && location.pathname !== '/passcode' && !!userInfo) {
			window.location.href = '/passcode';
		}
	}, [location]);
};

export default useTrackPincodeEnabled;
