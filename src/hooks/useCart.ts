import { isDiscountValid } from './../utils/isDiscountValid';
import { useAddToCart, useUpdateCart } from '../integrations/react_query/mutations';

import { saveSession, getSession } from '../utils/storage';

import { genId } from './../utils/genId';
import { CartProps, ECart, FlattenedProductProps, SalesProps } from '../models';

import React from 'react';
import { getSessionInfo } from '../utils/getSessionInfo';

import lodashThrottle from 'lodash.throttle';

type ThrottledFunction<T extends (...args: any[]) => any> = (...args: Parameters<T>) => void;

export function throttle<T extends (...args: any[]) => any>(func: T, limit: number): ThrottledFunction<T> {
  let lastRan = 0;
  let timeout: ReturnType<typeof setTimeout> | null = null;

  return function (...args: Parameters<T>) {
    const now = Date.now();

    if (now - lastRan >= limit) {
      func(...args);
      lastRan = now;
    } else {
      if (timeout) clearTimeout(timeout);
      timeout = setTimeout(() => {
        func(...args);
        lastRan = Date.now();
        timeout = null;
      }, limit);
    }
  };
}

export function debounce<T extends (...args: any[]) => any>(func: T, delay: number): ThrottledFunction<T> {
  let timeout: ReturnType<typeof setTimeout> | null = null;
  return function (...args: Parameters<T>) {
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), delay);
  };
}

export const useCart = (event?: 'change' | 'click', ref?: any) => {
  const [isAddingToCart, setIsAddingToCart] = React.useState(false);

  const handleAddingToCartStatus = (status: boolean) => {
    setIsAddingToCart(status);
  };

  const { mutate: mutateAdd } = useAddToCart(handleAddingToCartStatus);

  const { mutate: mutateUpdate } = useUpdateCart(handleAddingToCartStatus, event);

  const { userInfo, cartId } = getSessionInfo();

  //tight coupling (update and create) exist here due to  the workflow of adding product items to cart

  const handleAddToCart = async (item: FlattenedProductProps & { quantity: number; refCartId?: string }) => {
    //TODO: check the flow of params being sent in
    const {
      variantId,
      productName,
      pricing,
      merchantName,
      sku,
      // refCartId,
      quantity = 1,
      _id: productId,
      productId: productNo,
    } = item;

    const unitCost = item?.inventory?.unitCost;

    // const product = {
    //   productId,
    //   productName,
    //   sellingPrice: pricing.sellingPrice,
    //   discount: pricing.discountPercentage,
    //   quantity,
    //   variantId
    // };

    //TODO: figure out the payload to be passed while updating,
    //then go to the next task, by updating the sales payload and ensure it's solid enough against mutation

    const isDiscountValidStatus = isDiscountValid(pricing);

    const discountAmount = isDiscountValidStatus ? pricing?.discountAmount : 0;

    const cartItemUpdate: SalesProps = {
      productId,
      productNo: productNo || null,
      productName: productName?.trim(),
      sku,
      variantId: variantId ?? null,
      sellingPrice: pricing?.sellingPrice ?? null,
      discount: discountAmount ?? null,
      quantity: 1,
      unitCost: unitCost ?? null,
      refCartId: (getSession(`${cartId}-${userInfo?.profile?.email}`) as string) ?? null,
      isVoid: false,
      isDiscountValid: isDiscountValidStatus
      // pricing,
    };

    if (!getSession(`${cartId}-${userInfo?.profile?.email}`)) {
      saveSession(`${cartId}-${userInfo?.profile?.email}`, genId());

      //todo: try to ascertain that the corresponding session, if not found, then try to delete this created session, and then create another session

      //todo: cart instance is undefined, it instantiates without having corresponding user details(undefined)
      //in the db instance

      const cartItem: SalesProps = {
        productId,
        productNo: productNo || null,
        productName,
        sku,
        variantId: variantId ?? null,
        sellingPrice: pricing?.sellingPrice ?? null,
        discount: pricing?.discountAmount ?? pricing ?? null,
        quantity: 1,
        unitCost: unitCost ?? null,
        refCartId: (getSession(`${cartId}-${userInfo?.profile?.email}`) as string) ?? null,
        isVoid: false,
        isDiscountValid: isDiscountValidStatus
        // pricing,
      };

      //todo: review the _id props
      const payload: CartProps = {
        _id: getSession(`${cartId}-${userInfo?.profile?.email}`) as string,
        cashier: {
          name: `${userInfo?.profile?.lastName} ${userInfo?.profile?.firstName}`,
          id: userInfo?._id as string,
        },
        merchantName,
        type: ECart.cartType,
        status: ECart.active,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        products: [cartItem],
        customer: null,
        note: null,
        // pricing,
      };

      mutateAdd(payload);
    } else {
      //note no need to really get refcart here, since the cart obj has it already
      const refCartId = getSession(`${cartId}-${userInfo?.profile?.email}`) as string;

      if (!refCartId) {
        //todo: remove this
        alert('ref cart not found');
        return;
      }

      if (event === 'change') {
        //refcartid will always exist at this level
        //TODO: changed item to product
        mutateUpdate({ quantity, productId, variantId, refCartId: refCartId as string } as any);
        return;
      }

      mutateUpdate({ ...cartItemUpdate, refCartId });
    }
  };
  return { handleAddToCart: lodashThrottle(handleAddToCart, 1000), isAddingToCart };
};
