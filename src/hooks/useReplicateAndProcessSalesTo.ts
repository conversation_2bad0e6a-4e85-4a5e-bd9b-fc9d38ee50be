import React from 'react';
import { remoteDB, salesDB } from '../integrations/pouch/DBConn';
import { axiosInstanceShoppaBackend } from '../integrations/axios.config';
import { getPendingAndProcessingSales } from '../integrations/react_query/queryFn';
import { useOnlineStatus } from './useOnline';
import { getSessionInfo } from '../utils/getSessionInfo';
import { useAuthenticate } from './useAuthenticate';
import { handleCouchError } from '../utils/handleCouchError';

export const supportWebWorker = () => {
	return !!window.Worker;
};

export const useReplicateAndProcessSales = () => {
	const { userInfo } = getSessionInfo();

	const retrySync = async (salesData: { [key: string]: any }) => {
		try {
			await axiosInstanceShoppaBackend.post('/product/process-sales', { sale: salesData }, { withCredentials: true });
		} catch (error) {
			console.log(error, 'error replacating');
		}
	};

	const replicateSalesRef = React.useRef<PouchDB.Replication.Replication<{}> | null>(null);
	const liveReplicationRef = React.useRef<PouchDB.Replication.Replication<{}> | null>(null);

	const isOnline = useOnlineStatus();

	const processSalesWhenOnline = async () => {
		try {
			if (!isOnline) return;

			const salesDataResponse = await getPendingAndProcessingSales();

			if (salesDataResponse?.length <= 0) return;

			await Promise.all(salesDataResponse.map((sale: any) => retrySync(sale)));
		} catch (err) {
			console.log(err);
		}
	};

	const onlineReplicationRef = React.useRef<PouchDB.Replication.Replication<{}> | null>(null);

	const replicateAndProcessWhenOnline = () => {
		onlineReplicationRef.current = salesDB()
			.replicate.to(remoteDB(userInfo?.merchantName), { retry: true })
			.on('complete', async () => {
				await processSalesWhenOnline();
			});
	};

	const isAuthenticated = useAuthenticate();

	React.useEffect(() => {
		if (!isAuthenticated) {
			return;
		}

		replicateSalesRef.current = salesDB()
			.replicate.to(remoteDB(userInfo?.merchantName), { retry: true })
			.on('complete', async (info) => {
				if (info.status === 'complete') {
					liveReplicationRef.current = salesDB()
						.replicate.to(remoteDB(userInfo?.merchantName), { live: true, retry: true })
						.on('error', (info) => {});
					//manual process sales on mount
					if (!supportWebWorker()) {
						if (!isOnline) return;

						processSalesWhenOnline();
					}
				}
			})
			.on('error', (err: any) => {
				console.log(err);
				handleCouchError(err);
			})
			.on('change', (info) => {});

		//if browser does not supoort web worker
		if (!supportWebWorker()) {
			window.addEventListener('online', replicateAndProcessWhenOnline);
		}

		return () => {
			if (!supportWebWorker()) {
				window.removeEventListener('online', replicateAndProcessWhenOnline);
			}

			if (replicateSalesRef.current) {
				replicateSalesRef.current.cancel();
			}

			if (liveReplicationRef.current) {
				liveReplicationRef.current.cancel();
			}

			if (onlineReplicationRef.current) {
				onlineReplicationRef.current.cancel();
			}
		};
	}, []);
};
