import React from 'react';

import { useAuthenticate } from './useAuthenticate';

import { genericMerchantDB, remoteDB } from '../integrations/pouch/DBConn';
import { getSessionInfo } from '../utils/getSessionInfo';
import { handleCouchError } from '../utils/handleCouchError';
interface IReplicator {
	localDBName: string;
	handleReplicationStatus: (v: boolean) => void;
	updateOnChange: () => Promise<void>;
}

export const useReplicatorTo = ({ localDBName, handleReplicationStatus }: IReplicator) => {
	const [isReplicating, setIsReplicating] = React.useState(false);

	const replicateRef = React.useRef<any>(null);

	const liveReplicationRef = React.useRef<any>(null);

	const isAuthenticated = useAuthenticate();

	React.useEffect(() => {
		if (!isAuthenticated) {
			return;
		}

		handleReplicationStatus(true);
		setIsReplicating(true);

		const { userInfo } = getSessionInfo();

		replicateRef.current = genericMerchantDB(localDBName)
			.replicate.to(remoteDB(userInfo?.merchantName), {})
			.on('complete', (info) => {
				if (info.status === 'complete') {
					handleReplicationStatus(false);
					setIsReplicating(false);

					liveReplicationRef.current = genericMerchantDB(localDBName)
						.replicate.to(remoteDB(userInfo?.merchantName), { live: true, retry: true })
						.on('error', (info) => {})
						.on('change', async (info: any) => {
							//TODO: handle change callbacks explicit
						});
				}
			})
			.on('error', (error) => {
				handleCouchError(error);
			})
			.on('change', (info) => {})
			.on('denied', () => {});

		return () => {
			replicateRef.current && replicateRef.current?.cancel();

			liveReplicationRef.current && liveReplicationRef?.current.cancel();
		};
	}, []);

	return { isReplicating };
};
