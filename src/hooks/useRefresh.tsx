import { Button } from '../components/ui/button';
import { useQueryClient } from 'react-query';
import { LucideRefreshCw } from 'lucide-react';

export const useRefresh = (queryKeys: string[]) => {
	const queryClient = useQueryClient();

	const handleSalesRefresh = () => {
		queryKeys.forEach((queryKey) => {
			queryClient.invalidateQueries({ queryKey: [queryKey] });
		});
	};

	const Component = (
		<Button variant="ghost" onClick={handleSalesRefresh}>
			<LucideRefreshCw color="#A63494" />
		</Button>
	);

	return {
		Component,
		handleSalesRefresh,
	};
};
