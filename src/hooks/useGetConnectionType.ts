import { useSyncExternalStore } from 'react';
import { getConnectionType } from '../utils/getConnectionType';

function subscribe(callback: () => void) {
	if ('connection' in navigator) {
		(navigator as any).connection.addEventListener('change', callback);

		return () => {
			(navigator as any).connection.removeEventListener('change', callback);
		};
	}

	return () => {};
}

export const useGetConnectionType = () => {
	const connectionType = useSyncExternalStore(subscribe, getConnectionType);
	return connectionType;
};
