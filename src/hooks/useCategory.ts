import { FlattenedProductProps } from '../models';

export const useCategory = (data: FlattenedProductProps[] = []) => {
  if (!data.length) return {};

  const allCategories = data.flatMap((product) => product.categories || []).filter(Boolean);

  const categoryCounts = allCategories.reduce(
    (counts, category) => {
      counts[category] = (counts[category] || 0) + 1;
      return counts;
    },
    {} as Record<string, number>
  );

  let sum = 0;
  Object.entries(categoryCounts).forEach(([, v]) => {
    sum += v;
  });

  return { all: data?.length, ...categoryCounts };
};
