import { inventoryDB, remoteDB, salesDB } from '../integrations/pouch/DBConn';
import React from 'react';
import { DocumentPrefixes, SalesStatus, SalesTransactionResponseProps } from '../models';
import { getCurrentCart } from '../integrations/react_query/queryFn';
import { useOnlineStatus } from './useOnline';
import { getSessionInfo } from '../utils/getSessionInfo';
import {
	calculateOutstandingAmountWhenCreditOrPaid,
	calculateOverpaidAmount,
	calculateTotalAmount,
	calculateTotalAmountWithDiscount,
	calculateTotalAmountWithTax,
	calculateTotalDiscount,
	calculateTotalPayableAmount,
	calculateTotalPayments,
	calculateTotalTaxAmount,
} from '../utils/calcTotals';
import { UserResponseProps } from '../types';
import { generateUniqueID } from '../utils/generateUniqueId';
import { generateTransactionId } from '../utils/generateTransactionId';
import { handleCouchError } from '../utils/handleCouchError';
import { composeSingleInventoryLog } from '../utils/inventoryLog';

export const useCompleteSales = () => {
	const isOnline = useOnlineStatus();

	const transactionId = React.useRef<string>(generateTransactionId());

	const worker = new Worker(new URL('.././workers/syncAndProcessSales.ts', import.meta.url));

	const completeTransaction = async () => {
		const { userInfo, userLocationObject, companyInfo } = getSessionInfo();
		const cartItems = await getCurrentCart();

		if (!cartItems) {
			throw new Error('Cart item is required');
		}

		if (!userLocationObject) {
			throw new Error('Can not complete this sales, try again');
		}

		const outstandingAmount = +calculateOutstandingAmountWhenCreditOrPaid(
			calculateTotalAmountWithDiscount(cartItems.products),
			calculateTotalPayments(cartItems.payments ?? []),
			cartItems?.payments ?? []
		);

		const saleData: Omit<SalesTransactionResponseProps, 'payments'> & {
			totalAmount: number;
			totalAmountWithTax: number;
			amountPaid: number;
			outstandingAmount: number;
			discountTotal?: number;
			taxTotal?: number;
			overPaidAmount: number;
			isVoid: boolean;
			assignedLocation: UserResponseProps['assignedLocations'][0];
			payableAmount: number;
			taxPercentage: number;
		} = {
			...cartItems,
			products: cartItems?.products?.map((cartItemsProduct) => {
				return {
					...cartItemsProduct,
					tax: +calculateTotalTaxAmount([cartItemsProduct]),
					totalAmount: +calculateTotalAmount([cartItemsProduct]),
					totalAmountWithTax: +calculateTotalAmountWithTax([cartItemsProduct]),
					totalDiscount: +calculateTotalDiscount([cartItemsProduct]),
					payableAmount: +calculateTotalPayableAmount([cartItemsProduct]),
				};
			}),
			note: null,
			salesPerson: {
				name: `${userInfo?.profile?.firstName} ${userInfo?.profile?.lastName}`,
				id: userInfo?._id as string,
			},
			status: SalesStatus.processing,
			createdAt: new Date().toISOString(),
			updatedAt: new Date().toISOString(),
			type: 'sales',
			_id: `sales-${String(transactionId.current)}`,
			transactionId: transactionId.current,
			assignedLocation: userLocationObject as UserResponseProps['assignedLocations'][0],
			payableAmount: +calculateTotalPayableAmount(cartItems?.products),
			totalAmountWithTax: +calculateTotalAmountWithTax(cartItems.products),
			totalAmount: +calculateTotalAmount(cartItems.products),
			discountTotal: +calculateTotalDiscount(cartItems.products),
			invoiceId: generateUniqueID(DocumentPrefixes.INVOICE),
			salesId: generateUniqueID(DocumentPrefixes.SALES_ORDER),
			taxTotal: +calculateTotalTaxAmount(cartItems?.products),
			// amountPaid: +calculateTotalPayments(cartItems?.payments || []),
			amountPaid:
				outstandingAmount > 0
					? +calculateTotalPayments(cartItems?.payments || []) - outstandingAmount
					: +calculateTotalPayments(cartItems?.payments || []),
			isVoid: false,
			outstandingAmount: +calculateOutstandingAmountWhenCreditOrPaid(
				calculateTotalAmountWithDiscount(cartItems.products),
				calculateTotalPayments(cartItems.payments ?? []),
				cartItems?.payments ?? []
			),
			// outstandingAmount: +calculateOutstandingAmountWithTax(
			// 	calculateTotalAmountWithDiscount(cartItems.products),
			// 	calculateTotalPayments(cartItems.payments ?? []),
			// ),
			overPaidAmount: +calculateOverpaidAmount(
				calculateTotalAmountWithDiscount(cartItems.products),
				calculateTotalPayments(cartItems.payments ?? [])
			),
			taxPercentage: companyInfo?.tax ?? 0,
		};

		const inventoryLogs = saleData?.products.map((item) =>
			composeSingleInventoryLog({
				purchaseItem: {
					productId: item.productId,
					sellingPrice: item.sellingPrice,
					discount: item.discount ?? null,
					unitCost: item.unitCost,
					quantity: item.quantity,
					totalAmount: item.quantity * (item.sellingPrice || 1),
				},
				assignedLocation: userLocationObject as UserResponseProps['assignedLocations'][0],
				productName: item.productName,
				refCartId: item.refCartId,
				userFullName: saleData?.cashier?.name,
				userIdentity: saleData?.cashier?.id,
				userInfo: null,
				transactionStatus: 'sales',
				transactionType: 'stockOut',
				variantId: item.variantId?.toString(),
				referenceTransactionNo: saleData?.salesId,
				referenceTransactionId: saleData._id,
				note: null,
				sourceCreatedDate: saleData?.createdAt,
			})
		);

		// if (true) {
		// 	return
		// }
		try {
			await inventoryDB().bulkDocs(inventoryLogs);
			await salesDB().put(saleData);

			// if (!isOnline) return;

			await inventoryDB()
				.replicate.to(remoteDB(userInfo?.merchantName as string), { retry: true })
				.on('complete', async (info) => {
					if (info.status === 'complete') {
						worker.postMessage({ userInfo, status: 'process-now', salesPayload: saleData });
					}
				})
				.on('error', (err) => {
					console.log(err);
					handleCouchError(err);
				});

			await salesDB()
				.replicate.to(remoteDB(userInfo?.merchantName as string), { retry: true })
				.on('complete', async (info) => {
					if (info.status === 'complete') {
						worker.postMessage({ userInfo, status: 'process-now', salesPayload: saleData });
					}
				})
				.on('error', (err) => {
					console.log(err);
					handleCouchError(err);
				});
		} catch (err) {
			console.log(err);
		} finally {
		}
	};

	return {
		completeTransaction,
		transactionId: transactionId.current,
	};
};
