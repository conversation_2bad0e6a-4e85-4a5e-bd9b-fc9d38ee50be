import React from 'react';

import { useNavigate } from 'react-router-dom';

import { useAuthenticate } from './useAuthenticate';

import { EQuerykeys, IFilter } from './../models/index';

import { genericMerchantDB, remoteDB } from '../integrations/pouch/DBConn';
import { useQueryClient } from 'react-query';
import { getSessionInfo } from '../utils/getSessionInfo';
import { handleCouchError } from '../utils/handleCouchError';
interface ISync {
	localDBName: string;
	filter: IFilter;
	filterFunc: (v: { [key: string]: unknown }) => { [key: string]: unknown };
	handleSyncStatus: (v: boolean) => void;
}

export const useSync = ({ localDBName, filter, filterFunc, handleSyncStatus }: ISync) => {
	const [isReplicating, setIsReplicating] = React.useState(false);

	const navigate = useNavigate();

	const replicateRef = React.useRef<any>(null);

	const liveReplicationRef = React.useRef<any>(null);

	const isAuthenticated = useAuthenticate();

	const queryClient = useQueryClient();

	React.useEffect(() => {
		if (!isAuthenticated) {
			navigate('/login', { replace: true });
			return;
		}

		handleSyncStatus(true);
		setIsReplicating(true);

		const { userInfo } = getSessionInfo();

		replicateRef.current = genericMerchantDB(localDBName)
			.sync(remoteDB(userInfo?.merchantName), filterFunc({ ...filter, live: undefined, retry: true }))
			.on('complete', (info) => {
				if (info.pull?.status === 'complete') {
					handleSyncStatus(false);
					setIsReplicating(false);

					liveReplicationRef.current = genericMerchantDB(localDBName)
						.sync(remoteDB(userInfo?.merchantName), filterFunc({ ...filter, live: true, retry: true }))
						.on('error', (info) => {})
						.on('change', (info: any) => {
							queryClient.invalidateQueries(EQuerykeys.product);
						});
				}
			})
			.on('error', (error) => {
				handleCouchError(error);
			})
			.on('change', () => {})
			.on('denied', () => {});

		return () => {
			replicateRef.current && replicateRef.current?.cancel();

			liveReplicationRef.current && liveReplicationRef?.current.cancel();
		};
	}, []);

	return { isReplicating };
};
