import React from 'react';

import ReactDOM from 'react-dom';

import { IPopUp } from '../../models';

import { motion, AnimatePresence } from 'framer-motion';

import styles from './index.module.scss';

const PopUp: React.FC<IPopUp> = ({ show, withBg = true, setShow, children, ...props }) => {
	return show
		? ReactDOM.createPortal(
				<AnimatePresence>
					{show && (
						<motion.section
							className={`position-absolute ${withBg ? styles.wrapper : styles.wrapperNoBg} w-100`}
							{...props}
						>
							<div className="position-relative">{children}</div>
						</motion.section>
					)}
				</AnimatePresence>,
				document.body
			)
		: null;
};

export default React.memo(PopUp);
