import React from 'react';
import { IPopUpPosition } from '../../../models';
import { useClickOutside } from '../../../hooks/useClickOutside';

const PopUpPosition = ({
	x,
	y,
	width,
	styleClass,
	children,
	setShow,
}: IPopUpPosition & { setShow: React.Dispatch<React.SetStateAction<boolean>> }) => {
	const ref = React.useRef<any>(null);

	useClickOutside({
		ref,
		callback: () => {
			setShow(false);
		},
	});

	return (
		<div ref={ref} className={`position-absolute ${styleClass}`} style={{ top: y, left: x, width }}>
			{children}
		</div>
	);
};

export default React.memo(PopUpPosition);
