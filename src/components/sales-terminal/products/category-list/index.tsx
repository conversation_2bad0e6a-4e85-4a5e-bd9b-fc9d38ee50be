import React from 'react';

import styles from './index.module.scss';

import { motion } from 'framer-motion';
import { categoryListVariant } from '../../../../utils/anims';
import { categoryListChildrenVariant } from './../../../../utils/anims/index';
import { useCategory } from '../../../../hooks/useCategory';
import { FlattenedProductProps } from '../../../../models';

type CategoryListProps = {
	data: FlattenedProductProps[];
	setShow: (v: boolean) => void;
	setCategory: (v: string) => void;
	setSearchValue: (v: string) => void;
};

const CategoryList = ({ data, setShow, setCategory, setSearchValue }: CategoryListProps) => {
	const categories = useCategory(data);

	const ref = React.useRef('');

	const handlePickCategory = (key: string) => {
		ref.current = key;
		setCategory(key);
		setShow(false);
		setSearchValue('');
	};

	return (
		<motion.div
			variants={categoryListVariant}
			animate="visible"
			initial="hidden"
			exit="exit"
			className={`${styles.wrapper1} px-3 py-3`}
		>
			<motion.div className={`${styles.wrapper} px-2 px-md-2 px-lg-3 px-xl-3 px-xxl-3 py-3`}>
				{!Object.keys(categories).length
					? 'No Available Category'
					: Object.entries(categories).map(([key, value]: any) => {
							return (
								<motion.div
									variants={categoryListChildrenVariant}
									className={`${styles.list} d-flex justify-content-between py-4 px-2`}
									key={key}
									onClick={() => handlePickCategory(key)}
								>
									<div className={styles.name}>
										<span>{key}</span>
									</div>
									<div className={styles.quantity}>
										<span>
											{value} item{value > 1 ? 's' : ''}
										</span>
									</div>
								</motion.div>
							);
						})}
			</motion.div>
		</motion.div>
	);
};

export default CategoryList;
