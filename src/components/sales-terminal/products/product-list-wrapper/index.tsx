import React from 'react';
import styles from './index.module.scss';

export const ProductListWrapper = ({ children, style }: { children: React.ReactNode; style?: React.CSSProperties }) => {
	return (
		<section className={`${styles.wrapper} mt-4`} style={style}>
			<section className={`${styles.container} container-fluid overflow-hidden`}>{children}</section>
		</section>
	);
};

export default ProductListWrapper;
