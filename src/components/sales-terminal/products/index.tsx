import React from 'react';
import styles from './index.module.scss';
import CatergoriesBar from './categories';
import ProductNav from './product-nav';
import { useGetProducts } from '../../../integrations/react_query/queries';
import { EQuerykeys, FlattenedProductProps } from '../../../models';
import { useAppSelector } from '../../../store/storeConfig';
import ProductListWrapper from './product-list-wrapper';
import { useBarCodeScan } from '../../../hooks/useBarCodeScan';
import EmptyProductList from './empty-list';
import useDebounce from '../../../hooks/useDebounce';
import GridList from './product-list/grid/grid-list';
import { SearchBar } from './search-bar';
import ProductListItem from './product-list/list/list-item';

export const Products = () => {
	let filteredData: FlattenedProductProps[] | null = null;

	const {
		isReplicatingProduct,
		// isScanningBarcode
	} = useAppSelector((state) => state.productSlice);

	const [toggleView, setToggleView] = React.useState(true);

	const { data, isLoading, isError, refetch } = useGetProducts([EQuerykeys.product], {
		// enabled: isReplicatingProduct ? false : !isReplicatingProduct ? true : true,
		refetchOnMount: true,
		refetchOnReconnect: true,
		refetchOnWindowFocus: true,
		keepPreviousData: true,
		retry: true,
		refetchIntervalInBackground: true,
		staleTime: Infinity,
		cacheTime: Infinity,
	});

	//todo: examine the replication orthogonality

	React.useEffect(() => {
		if (!isReplicatingProduct) {
			refetch();
		}
	}, [isReplicatingProduct]);

	const [value, setSearchValue] = React.useState('');

	const kdbRef = React.useRef<HTMLInputElement | null>(null);

	const debounceValue = useDebounce(value, 0);

	const barcodeInputRef = useBarCodeScan({
		ref: kdbRef,
		setValue: setSearchValue,
		debounceValue,
	});

	const [category, setCategory] = React.useState<null | string>(null);

	if (category) {
		if (category === 'all') {
			filteredData = data ?? null;
		} else {
			filteredData = (data || []).filter((item) => {
				return item?.categories?.includes(category);
			});
		}
	}

	if (barcodeInputRef) {
		filteredData = (data || []).filter((item) => {
			return item?.barcode?.toString() === debounceValue?.toString();
		});
	}

	if (debounceValue && !barcodeInputRef) {
		filteredData = (data || []).filter((item) => {
			if (item?.barcode?.toString() === debounceValue?.toString()) {
				return true;
			} else {
				return (
					item?.productName?.toLocaleLowerCase()?.includes(debounceValue.toLocaleLowerCase()) ||
					item?.sku?.toLocaleLowerCase()?.includes(debounceValue.toLocaleLowerCase()) ||
					item?.description?.toLocaleLowerCase()?.includes(debounceValue.toLocaleLowerCase())
				);
			}
		});
	}

	const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		// if (kdbRef.current) {
		// 	kdbRef.current?.focus();
		// }

		setCategory('');
		setSearchValue(e.target.value);
	};

	React.useEffect(() => {
		if (kdbRef.current) {
			document.body.focus();
		}
	}, []);

	return (
		<section className={`${styles.wrapper} position-relative col-12 col-md-7 col-lg-7 col-xl-7 col-xxl-7`}>
			<section className={`${styles.title} mb-4`}>
				<span>Scan or Search Product</span>
			</section>

			<ProductNav toggle={toggleView} setToggle={setToggleView}>
				<SearchBar ref={kdbRef} onChange={handleChange} value={value} />
			</ProductNav>

			<ProductListWrapper>
				{isLoading ? (
					<section className="d-flex align-items-center justify-content-center" style={{ height: '30vh' }}>
						Pulling up data
					</section>
				) : isError ? (
					<div>An error occurred</div>
				) : (filteredData || []).length <= 0 || (!debounceValue && !category) ? (
					<EmptyProductList />
				) : (
					<>
						{toggleView ? (
							<div className="row h-100 justify-content-start">
								{(filteredData || []).map((product) => {
									return <GridList product={product} key={product._id} />;
								})}
							</div>
						) : (
							<div className="row align-items-center">
								{(filteredData || []).map((product) => {
									return <ProductListItem product={product} key={product?._id} />;
								})}
							</div>
						)}
					</>
				)}
			</ProductListWrapper>

			{!isLoading && !isError && (
				<CatergoriesBar
					{...{
						category,
						productData: data ?? [],
						setCategory,
						setSearchValue,
					}}
				/>
			)}
		</section>
	);
};

export default Products;
