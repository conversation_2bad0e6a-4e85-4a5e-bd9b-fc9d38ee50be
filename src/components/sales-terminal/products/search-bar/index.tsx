import React from 'react';
import SearchIconSvgSm from '../../../svgs/SearchIconSvgSm';

import styles from './index.module.scss';

type SearchBarProps = {
	onChange: React.ChangeEventHandler<HTMLInputElement>;
	value: string;
};

// ForwardedRef<unknown>;
export const SearchBar = React.forwardRef(
	({ onChange, value }: SearchBarProps, ref: React.LegacyRef<HTMLInputElement> | undefined) => {
		return (
			<section className={`${styles.wrapper} d-flex align-items-center flex-fill p-2`}>
				<aside className="">
					<SearchIconSvgSm />
				</aside>

				<aside className="mx-3 flex-fill">
					<input
						ref={ref}
						autoCorrect="false"
						spellCheck="false"
						placeholder="Scan or Search"
						type="text"
						value={value}
						onChange={onChange}
						className=" w-100"
						id="disable-autofill"
						name="disableAutofillFill"
						autoComplete="off"
					/>
				</aside>
			</section>
		);
	}
);
