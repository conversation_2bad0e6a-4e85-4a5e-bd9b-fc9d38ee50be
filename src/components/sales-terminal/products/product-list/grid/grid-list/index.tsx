import { DEFAULT_IMAGE_BASE64 } from '../../../../../../utils/msc';

import styles from './index.module.scss';
import { throttle, useCart } from '../../../../../../hooks/useCart';
import { EThrottles, FlattenedProductProps } from '../../../../../../models';
import { useOnlineStatus } from '../../../../../../hooks/useOnline';

type GridListProps = {
	product: FlattenedProductProps;
};

const GridList = ({ product }: GridListProps) => {
	const { handleAddToCart } = useCart();

	const isOnline = useOnlineStatus();

	const throttleHandleAddToCart = throttle(handleAddToCart, EThrottles.click);

	delete product?.attributes?.id;

	return (
		<section
			onClick={() => throttleHandleAddToCart({ ...product, quantity: 1 })}
			className={`${styles.wrapper} d-flex col-6 col-sm-4 col-md-6 col-lg-6 col-xl-4 col-xxl-4 my-2`}
		>
			<div className={`${styles.innerWrapper} d-flex align-content-stretch p-2`} style={{ flexDirection: 'column' }}>
				<div className={`d-flex flex-fill align-item-center justify-content-center ${styles.imgBg} px-4`}>
					<div className="d-flex align-items-center justify-content-center" style={{ width: '100%', height: '100%' }}>
						<img
							alt={product?.productName}
							src={!isOnline ? DEFAULT_IMAGE_BASE64 : product?.productImage || DEFAULT_IMAGE_BASE64}
							className="img-fluid"
						/>
					</div>
				</div>

				<div className="text-center px-1 my-2">
					<aside className={`d-flex align-items-center justify-content-center ${styles.title}`}>
						{product.productName}
					</aside>
					<aside className={`d-flex align-items-center justify-content-center capitalize ${styles.desc}`}>
						{product?.attributes ? Object.entries(product?.attributes).map(([k, v]) => `${k}, ${v}`) : null}

						{product?.attributes ? ' - ' : ''}
						{(product?.description || '')?.slice(0, 35) + '...'}
					</aside>

					<span
						className="px-10"
						style={{
							whiteSpace: 'pre-wrap',
						}}
					></span>
				</div>
			</div>
		</section>
	);
};

export default GridList;
