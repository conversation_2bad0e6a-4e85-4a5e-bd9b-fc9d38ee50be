@import "../../../../../../styles/_mixins.scss";

.wrapper {
  margin-bottom: 2em;
  cursor: pointer;

  .ml {
    margin-left: 25px;
  }

  .nmt {
    margin-top: 50px;
    background-color: purple !important;
  }
  .innerWrapper {
    width: 100%;
    border-radius: 16px;
    box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.06);
    background-color: var(--white-color);
    min-height: 30vh;

    .imgBg {
      background-color: var(--img-bg-color);
      text-align: center;
      justify-content: center;
      

      > img{
        object-fit: contain;
      }
    }

    .title {
      @include font("Josefin Sans", 0.9em, 500);
      @media screen and (max-width: 1000px) {
        @include font("Josefin Sans", 0.75em, 400);
      }
    }

    .desc {
      @include font("Josefin Sans", 0.8em, 400);
      opacity: 0.8;
      @media screen and (max-width: 1000px) {
        @include font("<PERSON><PERSON> Sans", 0.7em, 400);
      }
    }
  }
}
