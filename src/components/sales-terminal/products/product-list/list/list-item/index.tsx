import React from 'react';

import styles from './index.module.scss';

import { formatAmount } from '../../../../../../utils/funcs';
import { DEFAULT_IMAGE_BASE64 } from '../../../../../../utils/msc';
import { useCart } from '../../../../../../hooks/useCart';
import { FlattenedProductProps } from '../../../../../../models';
import { useOnlineStatus } from '../../../../../../hooks/useOnline';

type GridListProps = {
	product: FlattenedProductProps;
};

const ProductListItem = ({ product }: GridListProps) => {
	const { handleAddToCart } = useCart();

	const isOnline = useOnlineStatus();

	return (
		<section className={`${styles.wrapper} col-12 my-3`} onClick={() => handleAddToCart({ ...product, quantity: 1 })}>
			<div className={`${styles.innerWrapper} p-2 d-flex align-items-center justify-content-between`}>
				<div className={`${styles.imgBg} p-2`}>
					<img
						alt={product.productName}
						src={!isOnline ? DEFAULT_IMAGE_BASE64 : product.productImage || DEFAULT_IMAGE_BASE64}
						height="55px"
						width="75px"
						className="img-fluid"
					/>
				</div>

				<div className={`${styles.flex1} mx-3`}>
					<aside className={styles.title}>
						{product?.productName}, {product?.description}
					</aside>
				</div>

				<div className={`${styles.flex2} d-flex align-items-center justify-content-center`}>
					<aside className={styles.price}>&#8358;{formatAmount(product.pricing.sellingPrice ?? 0)}</aside>
				</div>
			</div>
		</section>
	);
};

export default ProductListItem;
