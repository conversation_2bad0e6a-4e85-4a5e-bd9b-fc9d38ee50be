import React from 'react';

//import { ListToggle } from "../list-toggle";

import { GridToggle } from './../grid-toggle/index';

import GridIconSvg from '../../../svgs/GridIconSvg';
import ListIconSvg from '../../../svgs/ListToggleIconSvg';

type ProductNavProps = {
	toggle: boolean;
	setToggle: (v: boolean) => void;
	children: React.ReactNode;
};

const ProductNav = ({ toggle, setToggle, children }: ProductNavProps) => {
	return (
		<section className="d-flex align-items-center">
			{children}
			<div>
				{toggle ? (
					<GridToggle onClick={() => setToggle(!toggle)}>
						<ListIconSvg />
					</GridToggle>
				) : (
					<GridToggle onClick={() => setToggle(!toggle)}>
						<GridIconSvg />
					</GridToggle>
				)}
			</div>
		</section>
	);
};

export default React.memo(ProductNav);
