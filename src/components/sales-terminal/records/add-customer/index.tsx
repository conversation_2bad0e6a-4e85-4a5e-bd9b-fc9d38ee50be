import React from 'react';

import { AddIconSvg } from '../../../svgs/AddIconSvg';
import PlaceholderIconSvg from '../../../svgs/PlaceholderIconSvg';

import styles from './index.module.scss';

import PopUpPosition from '../../../pop-up/pop-up-position';
import PopUp from '../../../pop-up';
import CustomerList from '../../../customer-list';

import usePopup from '../../../../hooks/usePopup';
import useWindowWidth from './../../../../hooks/useWindowWidth';

import Modal from '../../../resuable-ui/modal';

import CustomerForm from '../customer-form';
import SingleCustomerDetail from '../../../resuable-ui/single-customer-detail';
import { useQueryClient } from 'react-query';
import { CartProps, CustomerTransactionProps, ECart, EQuerykeys } from '../../../../models';
import { useDetachCustomerFromCart } from '../../../../integrations/react_query/mutations';
import useDebounce from '../../../../hooks/useDebounce';

const AddCustomer = () => {
	const ref = React.useRef<HTMLDivElement>(null);

	const { width } = useWindowWidth();

	const { x, y, show, setShow } = usePopup({
		ref,
		marginX: width <= 1000 ? 390 : 475,
		marginY: 90,
	});

	const { mutate: mutateDetachCustomerFromCart } = useDetachCustomerFromCart();

	const handleDetachCustomerFromCart = () => {
		mutateDetachCustomerFromCart();
	};

	const [showCustomerForm, setCustomerForm] = React.useState(false);

	const [currentCustomer, setCurrentCustomer] = React.useState<Omit<CustomerTransactionProps, 'name'> | undefined>(
		undefined
	);

	const [searchValue, setSearchValue] = React.useState('');

	const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		setSearchValue(e.target.value);
	};

	const queryClient = useQueryClient();

	const activeCartData: CartProps | undefined = queryClient.getQueryData([EQuerykeys.cart, ECart.active]);

	const isCustomer = activeCartData?.customer;

	const debounceSearchValue = useDebounce(searchValue, 600);

	const [lastName = '', firstName = ''] = activeCartData?.customer?.name?.split(' ') || [];

	// Determine customerData based on whether we're editing or adding
	const customerData = currentCustomer
		? {
				firstName,
				lastName,
				email: activeCartData?.customer?.email || '',
				id: activeCartData?.customer?.id || '',
				phoneNumber: activeCartData?.customer?.phoneNumber || '',
				birthday: activeCartData?.customer?.birthday || { month: '', day: '' },
			}
		: undefined;

	return (
		<>
			<Modal
				{...{
					show: showCustomerForm,
					setShow: setCustomerForm,
					columnLayout: 'col-11 col-lg-10 col-xl-8 col-xxl-8',
				}}
			>
				<CustomerForm customerData={customerData} closeCustomerForm={() => setCustomerForm(false)} />
			</Modal>

			<PopUp {...{ show: show && !!debounceSearchValue, setShow }}>
				<PopUpPosition
					{...{
						x,
						y,
						width: width <= 1000 ? '360px' : '450px',
						styleClass: `${styles.shadow} ${styles.rounded}`,
						setShow,
					}}
				>
					<CustomerList {...{ setShow, searchValue, debounceSearchValue }} />
				</PopUpPosition>
			</PopUp>
			<section ref={ref} className={`d-flex align-items-center justify-content-between ${styles.wrapper}`}>
				<div>
					<PlaceholderIconSvg width="40" height="40" viewBox="0 0 50 50" />
				</div>
				{isCustomer ? (
					<SingleCustomerDetail
						{...{
							data: activeCartData,
							handleClear: () => {
								handleDetachCustomerFromCart();
							},
							setEdit: () => {
								setCurrentCustomer({
									id: activeCartData?.customer?.id || '',
									email: activeCartData?.customer?.email || '',
									phoneNumber: activeCartData?.customer?.phoneNumber || '',
									birthday: activeCartData?.customer?.birthday || { month: '', day: '' },
								});
								setCustomerForm(true);
							},
						}}
					/>
				) : (
					<>
						<input
							id="disable-autofill"
							name="disableAutofillFill"
							placeholder="search customer"
							onChange={handleChange}
							onClick={() => setShow(show)}
							onFocus={() => setShow(true)}
							autoComplete="off"
						/>
						<button
							onClick={() => {
								setCurrentCustomer(undefined);
								setCustomerForm(true);
							}}
						>
							<AddIconSvg />
						</button>
					</>
				)}
			</section>
		</>
	);
};

export default AddCustomer;
