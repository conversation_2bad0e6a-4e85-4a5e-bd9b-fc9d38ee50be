import React from 'react';

import GiftCardSvg from '../../../svgs/GiftCardSvg';
import PromoIconSvg from '../../../svgs/PromoIconSvg';
import RightArrowIconSvg from '../../../svgs/RightArrowIconSvg';
import CouponIconSvg from './../../../svgs/CouponIconSvg';

import styles from './index.module.scss';

import { useNavigate } from 'react-router-dom';
import { useGetCart } from '../../../../integrations/react_query/queries';
import { ECart, EQuerykeys } from '../../../../models';
import { useCartAnalytics } from '../../../../hooks/useCartAnalytics';

const ActionButtons = () => {
	const navigate = useNavigate();

	const { data: cartItems } = useGetCart([EQuerykeys.cart, ECart.active]);

	const { payableAmount } = useCartAnalytics(cartItems?.products || []);

	const diablePayButton = (cartItems?.products ?? [])?.length <= 0 || +payableAmount <= 0;

	return (
		<div>
			<section
				className={`${styles.wrapper} h-100 d-flex justify-content-between align-items-stretch 
        `}
			>
				<div className="d-flex flex-fill">
					<button disabled={true}>
						<CouponIconSvg />
						<div>Coupon</div>
					</button>

					<button disabled={true}>
						<GiftCardSvg />
						<div>Giftcard</div>
					</button>

					<button disabled={true}>
						<PromoIconSvg />
						<div>Promo Code</div>
					</button>
				</div>
				<div className="d-flex flex-fill">
					<button
						disabled={diablePayButton}
						className="w-100 d-flex align-items-center justify-content-around"
						type="button"
						onClick={() => navigate('/payment')}
					>
						<div>Pay</div>
						<RightArrowIconSvg />
					</button>
				</div>
			</section>
		</div>
	);
};

export default ActionButtons;
