import React from 'react';

import ButtonIconText from '../../../resuable-ui/btn-icon-text';

import RetrieveIcon from '../../../svgs/RetrieveSvgIcon';
import ParkIcon from '../../../svgs/ParkIconSvg';
import DiscardIcon from '../../../svgs/DeleteIconSvg';

import Modal from '../../../resuable-ui/modal';

import { DiscardModalContent } from '../record-modals/discard';
import { ParkModalContent } from '../record-modals/park';
import { RetrieveModalContent } from '../record-modals/retrieve';
import { useGetCart, useGetParkedCart } from '../../../../integrations/react_query/queries';
import { ECart, EQuerykeys } from '../../../../models';

enum Etypes {
	park = 'park',
	retrieve = 'retrieve',
	discard = 'discard',
}

const SalesNav = () => {
	const [show, setShow] = React.useState(false);

	const [type, setType] = React.useState<string | null>(null);

	const handleType = (type: string) => {
		setType(type);
		setShow(true);
	};

	const { data: cartItems } = useGetCart([EQuerykeys.cart, ECart.active]);

	const { data: parkedCartData } = useGetParkedCart([EQuerykeys.cart, ECart.parked]);

	const disableDiscardAndParkButtons = (cartItems?.products ?? [])?.length <= 0;

	const disableRetriveSalesButton = (parkedCartData ?? [])?.length <= 0;

	return (
		<>
			<Modal
				{...{
					show,
					setShow,
					columnLayout: 'col-9 col-lg-8 col-xl-7 col-xxl-6',
				}}
			>
				{type === Etypes.park && <ParkModalContent {...{ setShow, customer: cartItems?.customer }} />}

				{type === Etypes.retrieve && <RetrieveModalContent {...{ setShow }} />}

				{type === Etypes.discard && <DiscardModalContent {...{ setShow }} />}
			</Modal>
			<section className="d-flex align-items-center justify-content-between">
				<ButtonIconText type="button" disabled={disableRetriveSalesButton} onClick={() => handleType(Etypes.retrieve)}>
					<RetrieveIcon id={Etypes.retrieve} />
					<span className="mx-1">Retrieve Sale</span>
				</ButtonIconText>

				<ButtonIconText type="button" disabled={disableDiscardAndParkButtons} onClick={() => handleType(Etypes.park)}>
					<ParkIcon id={Etypes.park} />
					<span className="mx-1" id={Etypes.park}>
						Park Sale
					</span>
				</ButtonIconText>

				<ButtonIconText
					type="button"
					disabled={disableDiscardAndParkButtons}
					onClick={() => handleType(Etypes.discard)}
				>
					<DiscardIcon />
					<span className="mx-1">Discard Sale</span>
				</ButtonIconText>
			</section>
		</>
	);
};

export default SalesNav;
