import React from 'react';

import styles from './index.module.scss';

import SalesNav from './sales-nav';
import AddCustomer from './add-customer';
import { CartItems } from './cart-items';

import CartBreakDown from '../../resuable-ui/cart-breakdown';
import ActionButtons from './action-btns';

import EmptyInCart from './empty-incart';

import { useGetCart } from '../../../integrations/react_query/queries';

import { ECart, EQuerykeys } from '../../../models';

const Records = () => {
	const { isLoading, isError, error, data } = useGetCart([EQuerykeys.cart, ECart.active]);

	return (
		<section className={`${styles.wrapper} col-12 col-md-5 col-lg-5 col-xl-5 col-xxl-5 p-3 px-lg-4 px-xl-4 px-xxl-4`}>
			<SalesNav />

			<AddCustomer />

			{isLoading ? (
				<section className="d-flex align-items-center justify-content-center" style={{ height: '30vh' }}>
					Pulling up data
				</section>
			) : error || isError ? (
				'Something went wrong.'
			) : !data?.products?.length ? (
				<EmptyInCart />
			) : (
				<>
					<CartItems {...{ data }} />
					<CartBreakDown />
				</>
			)}

			<ActionButtons />
		</section>
	);
};

export default Records;
