import React from 'react';

import styles from './index.module.scss';

import { Formik, Form, FormikHelpers, FormikProps } from 'formik';

import * as Yup from 'yup';

import { InputField } from '../../../resuable-ui/input';

import PopUp from '../../../pop-up';
import PopUpPosition from '../../../pop-up/pop-up-position';

import { CustomSelectDropDown } from '../../../resuable-ui/custom-select-dropdown';
import Button from './../../../resuable-ui/button/index';

import { stateLgs } from '../../../../data/stateLgs';
import states from '../../../../data/states';
import { months, getDaysForMonth } from '../../../../data/birthday';
import CalendarIconSvg from '../../../svgs/CalendarIconSvg';
import { CustomSelect } from '../../../resuable-ui/custom-select-field';

import useWindowWidth from '../../../../hooks/useWindowWidth';
import usePopup from '../../../../hooks/usePopup';

import { motion } from 'framer-motion';
import { addCustomerVariant } from '../../../../utils/anims';
import { useAddCustomer, useEditCustomer } from '../../../../integrations/react_query/mutations';
import { CustomerPayload } from '../../../../integrations/react_query/queryFn';

const validationSchema = Yup.object().shape({
	firstName: Yup.string().required('First name required'),
	lastName: Yup.string().required('Last name required'),
	email: Yup.string().email('Email is not valid').required('Email required'),
	phoneNumber: Yup.string().required('Phone required'),
	birthday: Yup.object()
		.shape({
			month: Yup.string().optional(),
			day: Yup.string().optional(),
		})
		.optional(),
	state: Yup.string().required('State required').optional(),
	city: Yup.string().required('City required').optional(),
});

type TUserValues = {
	firstName: string;
	lastName: string;
	email: string;
	phoneNumber: string;
	birthday: {
		month: string;
		day: string;
	};
	state: string;
	city: string;
};

const getInitialValue = (data?: CustomerPayload) => {
	return {
		firstName: data?.firstName ?? '',
		lastName: data?.lastName ?? '',
		email: data?.email ?? '',
		phoneNumber: data?.phoneNumber ?? '',
		birthday: {
			month: data?.birthday?.month ?? '',
			day: data?.birthday?.day ?? '',
		},
		state: '',
		city: '',
	};
};

const CustomerForm = ({
	customerData,
	closeCustomerForm,
}: {
	customerData?: CustomerPayload;
	closeCustomerForm: () => void;
}) => {
	const handleAddCustomer = async (
		{ email, lastName, firstName, phoneNumber, birthday }: TUserValues,
		actions: FormikHelpers<TUserValues>
	) => {
		await mutateAddCustomer({
			email,
			firstName,
			lastName,
			phoneNumber,
			birthday,
		});

		closeCustomerForm();
	};

	const { mutateAsync: mutateAddCustomer, isLoading: isAddingCustomer } = useAddCustomer();

	const { mutateAsync: mutateEditCustomer, isLoading: isEditingCustomer } = useEditCustomer();

	const handleEditCustomer = async (
		{ email, lastName, firstName, phoneNumber, birthday }: TUserValues,
		actions: FormikHelpers<TUserValues>
	) => {
		await mutateEditCustomer({
			id: customerData?.id as string,
			email,
			firstName,
			lastName,
			phoneNumber,
			birthday,
		});

		closeCustomerForm();
	};

	const stateRef = React.useRef(null);

	const cityRef = React.useRef(null);

	const { width } = useWindowWidth();

	const {
		x,
		y,
		show: showState,
		setShow: setShowState,
	} = usePopup({
		ref: stateRef,
		marginX: width <= 1000 ? 50 : -250,
		marginY: width <= 1000 ? 300 : 280,
	});

	const {
		x: x2,
		y: y2,
		show: showCity,
		setShow: setShowCity,
	} = usePopup({
		ref: cityRef,
		marginX: width <= 1000 ? 50 : -250,
		marginY: width <= 1000 ? 300 : 280,
	});

	// Birthday dropdown refs and state
	const birthdayMonthRef = React.useRef(null);
	const birthdayDayRef = React.useRef(null);

	const {
		x: x3,
		y: y3,
		show: showBirthdayMonth,
		setShow: setShowBirthdayMonth,
	} = usePopup({
		ref: birthdayMonthRef,
		marginX: width <= 1000 ? 50 : -250,
		marginY: width <= 1000 ? 300 : 280,
	});

	const {
		x: x4,
		y: y4,
		show: showBirthdayDay,
		setShow: setShowBirthdayDay,
	} = usePopup({
		ref: birthdayDayRef,
		marginX: width <= 1000 ? 50 : -250,
		marginY: width <= 1000 ? 300 : 280,
	});

	const isEditingMode = Object.values(customerData ?? {})?.filter(Boolean)?.length > 0;

	return (
		<>
			<motion.section
				variants={addCustomerVariant}
				initial="hidden"
				animate="visible"
				exit="exit"
				className={`${styles.wrapper} w-100 text-center position-relative`}
			>
				<div className={`${styles.title} text-center`}>{isEditingMode ? 'Edit Customer' : 'Add new customer'}</div>

				<Formik
					{...{
						initialValues: getInitialValue(customerData),
						validationSchema,
						onSubmit: isEditingMode ? handleEditCustomer : handleAddCustomer,
					}}
				>
					{(props: FormikProps<TUserValues>) => {
						return (
							<Form>
								<div className="d-flex align-items-start justify-content-between">
									<div className="flex-fill">
										<InputField
											onBlur={props.handleBlur}
											id="firstName"
											onChange={props.handleChange}
											type="text"
											value={props.values.firstName}
											error={props.errors.firstName}
											touched={props.touched.firstName}
											placeholder="First Name"
											name="firstName"
											showIcon={true}
										/>
									</div>
									<div className="flex-fill mx-3 mx-lg-4 mx-xl-4 mx-xxl-4">
										<InputField
											onBlur={props.handleBlur}
											id="lastName"
											onChange={props.handleChange}
											type="text"
											value={props.values.lastName}
											error={props.errors.lastName}
											touched={props.touched.lastName}
											placeholder="Last Name"
											name="lastName"
											showIcon={true}
										/>
									</div>
								</div>

								<div className="d-flex align-items-start justify-content-between my-4 my-lg-5 my-xl-5 my-xxl-5">
									<div className="flex-fill">
										<InputField
											onBlur={props.handleBlur}
											id="email"
											onChange={props.handleChange}
											type="text"
											value={props.values.email}
											error={props.errors.email}
											touched={props.touched.email}
											placeholder="Email"
											name="email"
											showIcon={true}
										/>
									</div>
									<div className="flex-fill mx-3 mx-lg-4 mx-xl-4 mx-xxl-4">
										<InputField
											onBlur={props.handleBlur}
											id="phoneNumber"
											onChange={props.handleChange}
											type="text"
											value={props.values.phoneNumber}
											error={props.errors.phoneNumber}
											touched={props.touched.phoneNumber}
											placeholder="Phone"
											name="phoneNumber"
											showIcon={true}
										/>
									</div>
								</div>

								{/* Birthday Row */}
								<div className="my-4 my-lg-5 my-xl-5 my-xxl-5">
									<div className="mb-3">
										<label
											className="d-flex align-items-center"
											style={{ color: '#7F5297', fontSize: '0.9em', fontWeight: 500 }}
										>
											<CalendarIconSvg style={{ marginRight: '8px' }} />
											Birthday
										</label>
									</div>
									<div className="d-flex align-items-start justify-content-between">
										{/* Month Selector */}
										<div className="flex-fill" ref={birthdayMonthRef} onClick={() => setShowBirthdayMonth(true)}>
											<CustomSelect
												onBlur={props.handleBlur}
												id="birthdayMonth"
												onChange={props.handleChange}
												type="text"
												value={props.values.birthday.month}
												error={props.errors.birthday?.month}
												touched={props.touched.birthday?.month}
												placeholder="Jan"
												name="birthday.month"
												showIcon={true}
												readOnly={true}
											/>
										</div>
										{/* Day Selector */}
										<div
											className="flex-fill mx-3 mx-lg-4 mx-xl-4 mx-xxl-4"
											ref={birthdayDayRef}
											onClick={() => setShowBirthdayDay(true)}
										>
											<CustomSelect
												onBlur={props.handleBlur}
												id="birthdayDay"
												onChange={props.handleChange}
												type="text"
												value={props.values.birthday.day}
												error={props.errors.birthday?.day}
												touched={props.touched.birthday?.day}
												placeholder="01"
												name="birthday.day"
												showIcon={true}
												readOnly={true}
											/>
										</div>
									</div>
								</div>

								{/* <div className="d-flex align-items-start justify-content-between my-3 my-lg-5 my-xl-5 my-xxl-5">
                  <div
                    className="flex-fill"
                    ref={stateRef}
                    onClick={() => setShowState(true)}
                  >
                    <CustomSelect
                      onBlur={props.handleBlur}
                      id="state"
                      onChange={props.handleChange}
                      type="text"
                      value={props.values.state}
                      error={props.errors.state}
                      touched={props.touched.state}
                      placeholder="State"
                      name="state"
                      showIcon={true}
                      readOnly={true}
                    />
                  </div>
                  <div
                    className="flex-fill mx-3 mx-lg-4 mx-xl-4 mx-xxl-4"
                    ref={cityRef}
                    onClick={() => props.values.state && setShowCity(true)}
                  >
                    <CustomSelect
                      onBlur={props.handleBlur}
                      id="city"
                      onChange={props.handleChange}
                      type="text"
                      value={props.values.city}
                      error={props.errors.city}
                      touched={props.touched.city}
                      placeholder="City"
                      name="city"
                      showIcon={true}
                      readOnly={true}
                      className={!props.values.state && styles.cursorDisabled}
                    />
                  </div>
                </div> */}

								<Button
									type="submit"
									disabled={isAddingCustomer || isEditingCustomer}
									styles="w-25 py-3 py-lg-3 py-xl-4 py-xxl-4"
								>
									{isEditingCustomer ? 'Editing...' : isAddingCustomer ? 'Saving...' : 'Save'}
								</Button>

								<PopUp
									{...{
										show: showState,
										setShow: setShowState,
										withBg: false,
									}}
								>
									<PopUpPosition
										{...{
											x,
											y,
											width: '224px',
											styleClass: `${styles.shadow} ${styles.rounded}`,
											setShow: setShowState,
										}}
									>
										<CustomSelectDropDown
											{...{
												type: 'state',
												setShow: setShowState,
												title: 'Select State',
												data: states,
												props,
												setSelected: props.setFieldValue,
												clearField: () => props.setFieldValue('city', ''),
												currentValue: props.values.state,
											}}
										/>
									</PopUpPosition>
								</PopUp>

								<PopUp {...{ show: showCity, setShow: setShowCity, withBg: false }}>
									<PopUpPosition
										{...{
											x: x2,
											y: y2,
											width: '224px',
											styleClass: `${styles.shadow} ${styles.rounded}`,
											setShow: setShowState,
										}}
									>
										<CustomSelectDropDown
											{...{
												title: 'Select City',
												setShow: setShowCity,
												data: stateLgs?.[props.values.state],
												props,
												type: 'city',
												setSelected: props.setFieldValue,
												currentValue: props.values.city,
											}}
										/>
									</PopUpPosition>
								</PopUp>

								{/* Birthday Month Popup */}
								<PopUp {...{ show: showBirthdayMonth, setShow: setShowBirthdayMonth, withBg: false }}>
									<PopUpPosition
										{...{
											x: x3,
											y: y3,
											width: '224px',
											styleClass: `${styles.shadow} ${styles.rounded}`,
											setShow: setShowBirthdayMonth,
										}}
									>
										<CustomSelectDropDown
											{...{
												title: 'Select Month',
												setShow: setShowBirthdayMonth,
												data: months,
												props,
												type: 'birthday.month',
												setSelected: (field: string, value: string) => {
													props.setFieldValue('birthday.month', value);
													// Clear day if month changes to ensure valid day selection
													if (props.values.birthday.month !== value) {
														props.setFieldValue('birthday.day', '');
													}
												},
												currentValue: props.values.birthday.month,
											}}
										/>
									</PopUpPosition>
								</PopUp>

								{/* Birthday Day Popup */}
								<PopUp {...{ show: showBirthdayDay, setShow: setShowBirthdayDay, withBg: false }}>
									<PopUpPosition
										{...{
											x: x4,
											y: y4,
											width: '224px',
											styleClass: `${styles.shadow} ${styles.rounded}`,
											setShow: setShowBirthdayDay,
										}}
									>
										<CustomSelectDropDown
											{...{
												title: 'Select Day',
												setShow: setShowBirthdayDay,
												data: props.values.birthday.month
													? getDaysForMonth(props.values.birthday.month)
													: getDaysForMonth('Jan'),
												props,
												type: 'birthday.day',
												setSelected: props.setFieldValue,
												currentValue: props.values.birthday.day,
											}}
										/>
									</PopUpPosition>
								</PopUp>
							</Form>
						);
					}}
				</Formik>
			</motion.section>
		</>
	);
};

export default CustomerForm;
