import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from 'react-query';
import CustomerForm from './index';

// Mock the hooks and dependencies
jest.mock('../../../../hooks/useWindowWidth', () => ({
	__esModule: true,
	default: () => ({ width: 1200 }),
}));

jest.mock('../../../../hooks/usePopup', () => ({
	__esModule: true,
	default: () => ({
		x: 0,
		y: 0,
		show: false,
		setShow: jest.fn(),
	}),
}));

jest.mock('../../../../integrations/react_query/mutations', () => ({
	useAddCustomer: () => ({
		mutateAsync: jest.fn(),
		isLoading: false,
	}),
	useEditCustomer: () => ({
		mutateAsync: jest.fn(),
		isLoading: false,
	}),
}));

const queryClient = new QueryClient({
	defaultOptions: {
		queries: { retry: false },
		mutations: { retry: false },
	},
});

const TestWrapper = ({ children }: { children: React.ReactNode }) => (
	<QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
);

describe('CustomerForm Birthday Selector', () => {
	const mockCloseCustomerForm = jest.fn();

	beforeEach(() => {
		jest.clearAllMocks();
	});

	test('renders birthday selector with month and day dropdowns', () => {
		render(
			<TestWrapper>
				<CustomerForm closeCustomerForm={mockCloseCustomerForm} />
			</TestWrapper>
		);

		// Check if birthday label with calendar icon is present
		expect(screen.getByText('Birthday')).toBeInTheDocument();

		// Check if month and day input fields are present
		expect(screen.getByPlaceholderText('Jan')).toBeInTheDocument();
		expect(screen.getByPlaceholderText('01')).toBeInTheDocument();
	});

	test('displays correct initial values for birthday fields', () => {
		const customerData = {
			id: '1',
			firstName: 'John',
			lastName: 'Doe',
			email: '<EMAIL>',
			phoneNumber: '**********',
			birthday: {
				month: 'May',
				day: '15',
			},
		};

		render(
			<TestWrapper>
				<CustomerForm customerData={customerData} closeCustomerForm={mockCloseCustomerForm} />
			</TestWrapper>
		);

		// Check if the birthday values are displayed correctly
		expect(screen.getByDisplayValue('May')).toBeInTheDocument();
		expect(screen.getByDisplayValue('15')).toBeInTheDocument();
	});

	test('handles empty birthday data gracefully', () => {
		const customerData = {
			id: '1',
			firstName: 'John',
			lastName: 'Doe',
			email: '<EMAIL>',
			phoneNumber: '**********',
		};

		render(
			<TestWrapper>
				<CustomerForm customerData={customerData} closeCustomerForm={mockCloseCustomerForm} />
			</TestWrapper>
		);

		// Check if empty birthday fields are handled correctly
		const monthField = screen.getByPlaceholderText('Jan');
		const dayField = screen.getByPlaceholderText('01');

		expect(monthField).toHaveValue('');
		expect(dayField).toHaveValue('');
	});
});

// Test the birthday utility functions
describe('Birthday Utilities', () => {
	test('getDaysForMonth returns correct number of days for each month', () => {
		const { getDaysForMonth } = require('../../../../data/birthday');

		// Test February (29 days)
		expect(getDaysForMonth('Feb')).toHaveLength(29);
		expect(getDaysForMonth('Feb')[28]).toBe('29');

		// Test April (30 days)
		expect(getDaysForMonth('Apr')).toHaveLength(30);
		expect(getDaysForMonth('Apr')[29]).toBe('30');

		// Test January (31 days)
		expect(getDaysForMonth('Jan')).toHaveLength(31);
		expect(getDaysForMonth('Jan')[30]).toBe('31');
	});

	test('getDaysForMonth returns zero-padded day strings', () => {
		const { getDaysForMonth } = require('../../../../data/birthday');

		const days = getDaysForMonth('Jan');
		expect(days[0]).toBe('01');
		expect(days[8]).toBe('09');
		expect(days[9]).toBe('10');
	});
});
