import React from 'react';

import { Formik, Form, FormikHelpers, FormikProps } from 'formik';

import styles from './index.module.scss';

import { InputField } from './../../../../resuable-ui/input/index';
import Button from '../../../../resuable-ui/button';

import RecordModalWrapper from '../../../../resuable-ui/generic-modal-wrapper';

import TextArea from './../../../../resuable-ui/text-area/index';
import { useParkCart } from '../../../../../integrations/react_query/mutations';
import { CustomerTransactionProps } from '../../../../../models';

type TParkValues = {
	note: string;
};

const initialValues: TParkValues = {
	note: '',
};

export const ParkModalContent = ({
	setShow,
	customer,
}: {
	setShow: (v: boolean) => void;
	customer: CustomerTransactionProps | null | undefined;
}) => {
	const { mutateAsync, isLoading } = useParkCart();

	const onSubmit = async (values: TParkValues, actions: FormikHelpers<TParkValues>) => {
		await mutateAsync(values.note);
		setShow(false);
	};

	return (
		<RecordModalWrapper title="Park Sales">
			<Formik {...{ initialValues, onSubmit }}>
				{(props: FormikProps<TParkValues>) => {
					return (
						<Form>
							<div className={styles.wrapper}>
								<div className="">
									<InputField
										onBlur={props.handleBlur}
										id=""
										onChange={props.handleChange}
										type="text"
										value={customer?.name || 'Walk in customer'}
										error={''}
										touched={''}
										placeholder=""
										name=""
										showIcon={true}
									/>
								</div>
								<div className="my-4">
									<TextArea
										className={styles.textArea}
										label="Note"
										name="note"
										onBlur={props.handleBlur}
										id="note-park"
										onChange={props.handleChange}
									/>
								</div>

								<div className="w-100 text-center my-3">
									<Button type="submit" disabled={isLoading} style={{ width: '40%' }} styles="py-4">
										{isLoading ? 'Parking...' : 'Continue'}
									</Button>
								</div>
							</div>
						</Form>
					);
				}}
			</Formik>
		</RecordModalWrapper>
	);
};
