import React from 'react';

import RecordModalWrapper from '../../../../resuable-ui/generic-modal-wrapper';

import WalkInList from '../../../../resuable-ui/walk-in-list';

import { useGetParkedCart } from '../../../../../integrations/react_query/queries';
import { ECart, EQuerykeys } from '../../../../../models';
import styles from './index.module.scss';

export const RetrieveModalContent = ({ setShow }: { setShow: (v: boolean) => void }) => {
	const { isLoading, isError, data } = useGetParkedCart([EQuerykeys.cart, ECart.parked]);

	return (
		<RecordModalWrapper
			title={() => {
				return (
					<div className="d-flex align-items-center justify-content-center">
						<div className={styles.title}>Retrieve Sales</div>

						<div className={styles.count}>{data?.length}</div>
					</div>
				);
			}}
			label={10}
		>
			{isLoading ? (
				<div>Loading sales to be retrieved</div>
			) : isError ? (
				<div>There was an error pulling retrieve</div>
			) : !data || (data || [])?.length <= 0 ? (
				<div>no parked sales</div>
			) : (
				<WalkInList data={data} setShow={setShow} />
			)}
		</RecordModalWrapper>
	);
};
