import React from 'react';

import Accordion from '../../../resuable-ui/accordion';
import DeleteIconSvg from '../../../svgs/DeleteIconSvg';
import RightArrowSmSvg from '../../../svgs/RightArrowSmSvg';

import { formatAmount } from '../../../../utils/funcs';
import styles from './index.module.scss';
import Modal from '../../../resuable-ui/modal';
import { DeleteModalContent } from './../record-modals/delete/';

import { useCart } from '../../../../hooks/useCart';
import { useDeleteFromCart } from './../../../../integrations/react_query/mutations';
import { CartProps, IDeleteCartItem } from '../../../../models';
import useDebounce from '../../../../hooks/useDebounce';
import { useAppSelector } from '../../../../store/storeConfig';

type THeader = {
	quantity: number;
	name: string;
	price: number | null;
	setExpanded: (v: string) => void;
	id: string;
	expanded: any;
	setShow: (arg: boolean) => void;
	setItem: (arg: { name: string; id: string; refCartId: string }) => void;
	handleDelete: (arg: IDeleteCartItem) => void;
	refCartId: string;
	ref: any;
};

const RenderHeader = React.forwardRef(
	({ quantity, name, price, expanded, setExpanded, id, setShow, setItem, refCartId }: THeader, ref: any) => {
		const { currentProductId } = useAppSelector((state) => state.productSlice);

		const isOpen = id === expanded;

		const handleExpand = (e: any) => {
			if (!(e.target.id === 'delete-icon')) {
				setExpanded(isOpen ? '' : id);
			}
		};

		React.useEffect(() => {
			if (ref.current && currentProductId) {
				ref.current.scrollIntoView({
					// behavoir: "smooth",
					block: 'nearest',
				});
			}
		}, [currentProductId]);

		return (
			<section
				ref={currentProductId === id ? ref : null}
				className="w-100 d-flex align-items-center justify-content-between py-2"
				onClick={handleExpand}
			>
				<div className="d-flex align-items-center">
					<div className={isOpen ? styles.rotate : styles.transit}>
						<RightArrowSmSvg />
					</div>

					<div className="mx-2 mx-lg-3 mx-xl-3 mx-xxl-3">{quantity || 0}</div>
					<div className="text-uppercase">{name}</div>
				</div>
				<div className="d-flex align-items-center">
					<div className="mx-1 mx-md-3 mx-lg-3 mx-xl-4 mx-xxl-4">
						{formatAmount((Number(quantity) || 0) * Number(price))}
					</div>
					<div
						id="delete-icon"
						onClick={() => {
							setShow(true);
							setItem({ name, id, refCartId });
						}}
					>
						<DeleteIconSvg id="delete-icon" />
					</div>
				</div>
			</section>
		);
	}
);

type TBody = {
	quantity: number;
	name: string;
	price: number | null;
	discount: number | null;
	productId: string;
	variantId: number | null;
	unitCost: number | null;
	refCartId: string;
	handleAddToCart: (v: any) => void;
};

const RenderBody = ({ quantity, variantId, price, discount, productId, handleAddToCart, refCartId }: TBody) => {
	const [changeQuantity, setChangeQuantity] = React.useState('');

	const debounceQuantity = useDebounce(changeQuantity, 50);

	React.useEffect(() => {
		if (changeQuantity === '') {
			return;
		}

		if (changeQuantity === 'e') {
			handleAddToCart({
				quantity: '',
				_id: productId,
				variantId,
				refCartId,
			});
			return;
		}

		handleAddToCart({
			quantity: Number(changeQuantity),
			_id: productId,
			variantId,
			refCartId,
		});
	}, [debounceQuantity]);

	const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		let value = e.target.value;

		if (isNaN(Number(value))) {
			return;
		}

		value = value.replace(/^0+(?=\d)/, '');

		setChangeQuantity(value === '' ? 'e' : value);
	};

	return (
		<section className={`${styles.bodyWrapper}`}>
			<div className={`${styles.tableBodyHeader} d-flex align-items-center justify-content-between`}>
				<div>Quantity</div>
				<div>Price (&#8358;)</div>
				<div>Discount (%)</div>
			</div>

			<div className={`d-flex align-items-center justify-content-between ${styles.tableBodyWrapper}`}>
				<div>
					<input
						value={changeQuantity === 'e' ? quantity : changeQuantity === '' ? quantity : changeQuantity || quantity}
						onChange={handleChange}
					/>
				</div>
				<div>{formatAmount(price ?? 0)}</div>
				<div>{discount}</div>
			</div>
		</section>
	);
};

export const CartItems = ({ data }: { data: CartProps }) => {
	const { handleAddToCart } = useCart('change');

	const { currentProductId } = useAppSelector((state) => state.productSlice);

	const { mutate: mutateDelete } = useDeleteFromCart();

	const [expanded, setExpanded] = React.useState<string>('');

	const [show, setShow] = React.useState(false);

	const handleDelete = (arg: IDeleteCartItem) => {
		mutateDelete(arg);
	};

	const [item, setItem] = React.useState<{
		name: string;
		id: string;
		refCartId: string;
	}>({
		name: '',
		id: '',
		refCartId: '',
	});

	const cartRef = React.useRef<any>(null);

	React.useEffect(() => {
		// const firstCartItem = (data || [])?.[0]?.productId;

		setExpanded(currentProductId as any);

		// if (cartRef.current) {
		//   cartRef.current.scrollTo({
		//     top: 0,
		//     behaviour: "smooth",
		//   });
		// }
	}, [currentProductId, data.products.length]);

	return (
		<>
			<Modal
				{...{
					show,
					setShow,
					columnLayout: 'col-9 col-lg-8 col-xl-7 col-xxl-6',
				}}
			>
				<DeleteModalContent {...{ item, setShow, handleDelete }} />
			</Modal>

			<section className={`${styles.wrapper}`}>
				{data?.products?.map(
					({ discount, productId, productName, quantity, refCartId, sellingPrice, unitCost, variantId }) => {
						return (
							<Accordion
								id={productId}
								key={productId}
								expanded={expanded}
								setExpanded={setExpanded}
								headerContext={() => (
									<RenderHeader
										{...{
											quantity,
											name: productName,
											price: sellingPrice,
											expanded,
											unitCost,
											setExpanded,
											id: productId,
											refCartId: refCartId as string,
											setShow,
											setItem,
											handleDelete,
										}}
										ref={cartRef}
									/>
								)}
								bodyContext={() => (
									<RenderBody
										{...{
											quantity,
											name: productName,
											price: sellingPrice,
											discount,
											unitCost,
											productId,
											handleAddToCart,
											variantId,
											refCartId: refCartId as string,
										}}
									/>
								)}
							/>
						);
					}
				)}
			</section>
		</>
	);
};
