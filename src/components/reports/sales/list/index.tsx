'use client';

import React from 'react';
import _ from 'lodash';
import { createColumnHelper } from '@tanstack/react-table';
import { format } from 'date-fns';
import { Button } from '../../../../components/ui/button';
import { SalesReportTransactionResponse } from '../types';
import { useGetSalesReport, useGetSalesReportSummary } from '../queries';
import ReportsFilter from '../filter';
import { DateRange } from 'react-day-picker';
import useBookmarkPagination from '../../../../hooks/useBookmarkPagination';
import ListTable from '../../../../components/list-table';
import { FilterBy } from '../../../../models';
import { ReportsIcon } from '../../../../components/svgs/reports';
import { downloadSalesReport } from '../../../../utils/downloadReport';
import Void from '../../../../components/void';
import { formatAmount } from '../../../../utils/funcs';
import { cn } from '../../../../lib/utils';

const columnHelper = createColumnHelper<SalesReportTransactionResponse>();

const columns = [
	columnHelper.accessor((row) => row, {
		id: 'createdAt',
		cell: (info) => (
			<aside className="text-1em mt-1 py-3">
				<span>{format(new Date(info.getValue().createdAt), 'Pp')}</span>
			</aside>
		),
		header: () => <span>Sales Date</span>,
		enableResizing: false,
		size: 310,
	}),
	columnHelper.accessor((row) => row, {
		id: 'invoice',
		cell: (info) => (
			<aside className="flex items-center text-1em mt-1">
				<span className="pr-1">{info.getValue()?.invoiceId}</span>
				<div>{info.getValue()?.isVoid && <Void className="py-1" />}</div>
			</aside>
		),
		header: () => <span>Invoice</span>,
		enableResizing: false,
		size: 300,
	}),
	columnHelper.accessor((row) => row, {
		id: 'items',
		cell: (info) => (
			<aside className={cn('text-1em mt-1')}>
				<span>{info.getValue()?.products?.[0]?.productName}</span>{' '}
				{info.getValue()?.products?.length > 1 && (
					<span className="bg-[#F8EAFF] text-[#8341A6] text-[12px] px-2 py-[5px] rounded-[10px]">
						+{info.getValue()?.products?.length - 1}
					</span>
				)}
			</aside>
		),
		header: () => <span>Items</span>,
		enableResizing: false,
		size: 300,
	}),
	columnHelper.accessor((row) => row, {
		id: 'customer',
		cell: (info) => (
			<aside className={cn('text-1em mt-1')}>
				<span>{info.getValue().customer?.name ?? '---'}</span>
			</aside>
		),
		header: () => <span>Customer</span>,
		enableResizing: false,
		size: 250,
	}),
	columnHelper.accessor((row) => row, {
		id: 'customer',
		cell: (info) => (
			<aside className={cn('text-1em mt-1')}>
				{info
					.getValue()
					?.payments?.map(({ method }) => method)
					.join(', ')}
			</aside>
		),
		header: () => <span>Payment method</span>,
		enableResizing: false,
		size: 250,
	}),
	columnHelper.accessor((row) => row, {
		id: 'Oustanding',
		cell: (info) => (
			<aside className="text-1em mt-1">
				<span>{info.getValue().cashier.name ?? '---'}</span>
			</aside>
		),
		header: () => <span>Sales Person</span>,
		enableResizing: false,
		size: 200,
	}),
	columnHelper.accessor((row) => row, {
		id: 'total',
		cell: (info) => (
			<aside className="text-1em mt-1">
				<span>{formatAmount(info.getValue().totalAmount ?? 0)}</span>
			</aside>
		),
		header: () => <span>Total</span>,
		enableResizing: false,
		size: 200,
	}),
	columnHelper.accessor((row) => row, {
		id: 'amount paid',
		cell: (info) => (
			<aside className="text-1em mt-1">
				<span>{formatAmount(info.getValue().amountPaid ?? 0)}</span>
			</aside>
		),
		header: () => <span>Amount Paid</span>,
		enableResizing: false,
		size: 200,
	}),
	columnHelper.accessor((row) => row, {
		id: 'Oustanding',
		cell: (info) => (
			<aside className="text-1em mt-1">
				<span>{formatAmount(info.getValue().outstandingAmount ?? 0)}</span>
			</aside>
		),
		header: () => <span>Outstanding</span>,
		enableResizing: false,
		size: 200,
	}),
];

const ReportsEmptyState = () => {
	return (
		<div className="text-center">
			<div className="flex justify-center mb-3">
				<ReportsIcon color="#C53030" />
			</div>

			<span className="font-semibold text-[#000000] text-[0.95em]">No reports available</span>
		</div>
	);
};

const SalesReportList = () => {
	const { currentBookmark, handleNextPage, handlePreviousPage } = useBookmarkPagination();

	const [selectedDate, setSelectedDate] = React.useState<DateRange | undefined>(undefined);

	const [filterBy] = React.useState<FilterBy | null>('today');

	const { data, isLoading, isError } = useGetSalesReport({
		bookmark: currentBookmark,
		filterBy: filterBy,
		endDate: selectedDate?.to,
		startDate: selectedDate?.from,
	});

	const handleSetSelectedDate = (d: DateRange | undefined) => {
		setSelectedDate(d);
	};

	const handleDownloadReport = () => {
		if ((!selectedDate?.from || !selectedDate?.to) && !filterBy) return;

		downloadSalesReport({
			filterBy,
			startDate: selectedDate?.from,
			endDate: selectedDate?.to,
		});
	};

	return (
		<section className="bg-white px-5 py-3 h-[93%] rounded-[10px]">
			<ReportsFilter
				downloadReport={handleDownloadReport}
				selectedDate={selectedDate}
				onDateSelect={handleSetSelectedDate}
			/>

			{/* <SalesReportSummary selectedDate={selectedDate} /> */}

			{isLoading ? (
				<div className="!h-[60vh] flex items-center justify-center">Kindly wait...</div>
			) : isError ? (
				<div className="!h-[60vh] flex items-center justify-center text-red-300">An error occured. Try again</div>
			) : (
				<>
					<div className="mt-5 h-[65vh] overflow-auto custom-scrollbar">
						<ListTable columns={columns} data={data?.data?.sales || []} emptyState={<ReportsEmptyState />} />
					</div>

					<div className="w-full flex justify-end flex-1">
						<Button
							className="py-4 px-4 bg-white hover:opacity-80 duration-75 ease-in-out hover:bg-white border text-[#171923] mx-2 text-[0.95em] border-[#A0AEC0] rounded-md"
							onClick={handlePreviousPage}
							disabled={!data?.data?.previousPageBookmark}
						>
							Previous
						</Button>

						<Button
							className="py-4 px-4 bg-white hover:bg-white hover:opacity-80 duration-75 ease-in-out border text-[#171923] text-[0.95em] mx-5 border-[#A0AEC0] rounded-md"
							onClick={() => handleNextPage(data?.data?.nextPageBookmark ?? undefined)}
							disabled={!data?.data?.nextPageBookmark}
						>
							Next
						</Button>
					</div>
				</>
			)}
		</section>
	);
};

export default SalesReportList;
