import { DateRange } from 'react-day-picker';
import { useGetSalesReportSummary } from '../queries';

type SalesReportSummaryProps = {
  selectedDate: DateRange | undefined;
};

const SalesReportSummary = ({ selectedDate }: SalesReportSummaryProps) => {
  const { data } = useGetSalesReportSummary({
    filterBy: 'today',
    endDate: selectedDate?.to,
    startDate: selectedDate?.from,
  });

  const salesSummary = data?.data?.salesSummary;

  return (
    <section className="mt-4 bg-[#EDF2F7] flex items-center justify-between text-[0.875em] rounded-b-lg px-5 py-2">
      <div>
        <span className="text-[#4A5568] font-normal">Total Sales:</span>
        <span className="font-bold text-[0.875rem] text-[#3B82F6] px-5">
          {salesSummary?.totalSales ?? 0}
        </span>
      </div>

      <div>
        <span className="text-[#4A5568] font-normal">Sales Total:</span>
        <span className="font-bold text-[0.875rem] px-5 text-[#3B82F6]">
          {salesSummary?.totalAmount ?? 0}
        </span>
      </div>

      <div>
        <span className="text-[#4A5568] font-normal">Amount Paid:</span>
        <span className="font-bold text-[0.875rem] px-5 text-[#3B82F6]">
          {salesSummary?.amountPaid ?? 0}
        </span>
      </div>

      <div>
        <span className="text-[#4A5568] font-normal">Outstanding:</span>
        <span className="font-bold text-[0.875rem] px-5 text-[#3B82F6]">
          {salesSummary?.outstandingAmount ?? 0}
        </span>
      </div>
    </section>
  );
};

export default SalesReportSummary;
