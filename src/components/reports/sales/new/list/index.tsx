'use client';

import React from 'react';
import _ from 'lodash';
import { createColumnHelper } from '@tanstack/react-table';
import { format } from 'date-fns';
import { Button } from '../../../../../components/ui/button';
import { SalesReportTransactionResponse } from '../../types';
import { useGetSalesReport } from '../../queries';
import ReportsFilter from '../../filter';
import { DateRange } from 'react-day-picker';
import useBookmarkPagination from '../../../../../hooks/useBookmarkPagination';
import ListTable from '../../../../../components/list-table';
import { FilterBy } from '../../../../../models';
import { downloadSalesReport } from '../../../../../utils/downloadReport';
import Void from '../../../../../components/void';
import { formatAmount } from '../../../../../utils/funcs';
import SalesDetailsNew from '../detail/index';
import { SearchIcon } from 'lucide-react';
import { Input } from '../../../../../components/ui/input';
import { ReportsEmptyState } from '../empty-state';

const columnHelper = createColumnHelper<SalesReportTransactionResponse>();

const SalesReportListNew = () => {
	const { currentBookmark, handleNextPage, handlePreviousPage } = useBookmarkPagination();

	const [selectedDate, setSelectedDate] = React.useState<DateRange | undefined>(undefined);

	const [filterBy] = React.useState<FilterBy | null>('today');

	const [searchTerm, setSearchTerm] = React.useState<string>('');

	const { data, isLoading, isError } = useGetSalesReport({
		searchTerm,
		bookmark: currentBookmark,
		filterBy: filterBy,
		endDate: selectedDate?.to,
		startDate: selectedDate?.from,
	});

	const handleSetSelectedDate = (d: DateRange | undefined) => {
		setSelectedDate(d);
	};

	const handleDownloadReport = () => {
		if ((!selectedDate?.from || !selectedDate?.to) && !filterBy) return;

		downloadSalesReport({
			filterBy,
			startDate: selectedDate?.from,
			endDate: selectedDate?.to,
		});
	};

	const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
		setSearchTerm(e.target.value);
	};

	const debounceHandleSearch = _.debounce(handleSearch, 500);

	const [salesData, setSalesData] = React.useState<SalesReportTransactionResponse | undefined>();

	const handleRowClick = (salesRow: SalesReportTransactionResponse) => {
		setSalesData(salesRow);
	};

	const columns = [
		columnHelper.accessor((row) => row, {
			id: 'invoice',
			cell: (info) => (
				<aside className="px-3 py-2 flex justify-between items-center cursor-pointer hover:bg-[#EDF2F7] transition-all duration-75 ease-linear text-1em mt-1">
					<div>
						<div className="flex items-center">
							<span className="pr-1 font-[500]">{info.getValue()?.invoiceId}</span>
							<div>{info.getValue()?.isVoid && <Void className="py-1" />}</div>
						</div>
						<span className="text-[#718096] text-[0.875rem]">{info.getValue()?.products?.[0]?.productName}</span>{' '}
						{info.getValue()?.products?.length > 1 && (
							<span className="bg-[#F8EAFF] text-[#8341A6] text-[12px] px-2 py-[5px] rounded-[10px]">
								+{info.getValue()?.products?.length - 1}
							</span>
						)}
					</div>

					<div>
						<aside className="text-1em font-[600]">
							<span>{formatAmount(info.getValue().totalAmount ?? 0)}</span>
						</aside>

						<aside className="text-1em mt-1 text-[#4A5568] font-[400]">
							<span>{format(new Date(info.getValue().createdAt), 'dd LLL yyyy h:mm a')}</span>
						</aside>
					</div>
				</aside>
			),
			header: () => <span></span>,
			enableResizing: false,
			size: 300,
		}),
	];

	return (
		<section className="px-5 py-3 h-[93%] rounded-[10px]">
			<ReportsFilter
				salesData={data?.data?.sales || []}
				downloadReport={handleDownloadReport}
				selectedDate={selectedDate}
				onDateSelect={handleSetSelectedDate}
			/>

			<div className="mt-5 grid grid-cols-12 bg-[#FAFAFB]">
				<div className="col-span-5 bg-[#FFFFFF]">
					<div className="relative rounded-md border border-gray-300 focus-within:border-[#A63494] focus-within:ring-[0.3px] focus-within:ring-[#A63494] focus-visible:outline-none focus-visible:border-0">
						<SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
						<Input
							type="text"
							placeholder="Search amount, receipt number, customer, date"
							className="w-[92.5%] ml-10 py-3 text-[0.8125em] focus:outline-none border-0 outline-none"
							onChange={debounceHandleSearch}
						/>
					</div>
					{isLoading ? (
						<div className="!h-[60vh] flex items-center justify-center">Kindly wait...</div>
					) : isError ? (
						<div className="!h-[60vh] flex items-center justify-center text-red-300">An error occured. Try again</div>
					) : (data?.data?.sales || [])?.length <= 0 ? (
						<div className="h-[65vh] flex justify-center items-center overflow-auto custom-scrollbar">
							<ReportsEmptyState />
						</div>
					) : (
						<>
							<div className="max-h-[65vh] overflow-auto custom-scrollbar">
								<ListTable
									handleRowClick={handleRowClick}
									columns={columns}
									data={data?.data?.sales || []}
									emptyState={<ReportsEmptyState />}
								/>
							</div>

							{(data?.data?.sales ?? []).length > 0 && (
								<div className="w-full flex justify-end flex-1">
									<div className="w-full flex justify-end flex-1 py-4">
										<Button
											className="py-2 px-4 bg-white hover:opacity-80 duration-75 ease-in-out hover:bg-white border text-[#171923] mx-2 text-[0.95em] border-[#A0AEC0] rounded-md"
											onClick={handlePreviousPage}
											disabled={!data?.data?.previousPageBookmark}
										>
											Previous
										</Button>

										<Button
											className="py-2 px-4 bg-white hover:bg-white hover:opacity-80 duration-75 ease-in-out border text-[#171923] text-[0.95em] mx-5 border-[#A0AEC0] rounded-md"
											onClick={() => handleNextPage(data?.data?.nextPageBookmark ?? undefined)}
											disabled={!data?.data?.nextPageBookmark}
										>
											Next
										</Button>
									</div>
								</div>
							)}
						</>
					)}
				</div>

				<div className="col-span-7 px-[30px] bg-[#FFFFFF]">
					<SalesDetailsNew salesData={salesData} />
				</div>
			</div>
		</section>
	);
};

export default SalesReportListNew;
