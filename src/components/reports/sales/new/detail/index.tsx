import { SalesReportTransactionResponse } from '../../types';
import { ReportsEmptyState } from '../empty-state';
import { format } from 'date-fns';

const SalesDetailsNew = ({ salesData }: { salesData: SalesReportTransactionResponse | undefined }) => {
	if (!salesData)
		return (
			<div className="h-[90vh]">
				<h3 className="text-[1rem] text-[#4A5568] text-center">Transaction Detail</h3>

				<div className="flex items-center self-center justify-center h-[60vh]">
					<ReportsEmptyState />
				</div>
			</div>
		);

	const salesitemLength = salesData?.products?.length;

	return (
		<div className="max-h-[65vh] overflow-auto custom-scrollbar px-5">
			<h3 className="text-[1rem] text-[#4A5568] text-center">Transaction Detail</h3>

			<div className="grid grid-cols-12">
				<div className="col-span-6">
					<h4 className="text-[0.875rem] text-[#4A5568]">Date</h4>
				</div>

				<div className="col-span-6 text-[0.875rem] text-right">
					{format(new Date(salesData.createdAt), 'dd LLL, yyyy h:mm a')}
				</div>
			</div>

			<div className="grid grid-cols-12">
				<div className="col-span-6">
					<h4 className="text-[0.875rem] text-[#4A5568]">Invoice</h4>
				</div>

				<div className="col-span-6 text-[0.875rem] text-right">{salesData?.invoiceId}</div>
			</div>

			<div className="grid grid-cols-12">
				<div className="col-span-6">
					<h4 className="text-[0.875rem] text-[#4A5568]">Customer Name</h4>
				</div>

				<div className="col-span-6 text-[0.875rem] text-[#A63494] text-right">{salesData?.customer?.name ?? 'N/A'}</div>
			</div>

			<div className="h-[1px] bg-[#CBD5E0] my-7"></div>

			<div>
				{salesData.products.map((product) => {
					return (
						<div className="grid grid-cols-12 mb-3">
							<div className="col-span-6 font-[600] text-[0.875rem]text-right">{product?.productName}</div>

							<div className="col-span-6 font-[700] text-[0.875rem]text-right flex justify-self-end ">
								{product?.totalAmount}
							</div>
						</div>
					);
				})}
			</div>

			<div className="h-[1px] bg-[#CBD5E0] my-7"></div>

			<div className="grid grid-cols-12">
				<h4 className="col-span-6 font-[600] text-[0.875rem]">Subtotal</h4>

				<div className="col-span-6 font-[700] text-[0.875rem] text-right">{salesData?.payableAmount}</div>
			</div>

			<div className="grid grid-cols-12">
				<h4 className="col-span-6 font-[600] text-[0.875rem]">VAT ({salesData?.taxPercentage ?? 0}%)</h4>

				<div className="col-span-6 font-[700] text-[0.875rem] text-right">{salesData?.taxTotal}</div>
			</div>

			<div className="grid grid-cols-12">
				<h4 className="col-span-6 font-[600] text-[0.875rem]">
					<span className="text-[#A63494]">Total </span>
					<span className="text-[#4A5568]">
						({salesitemLength} {salesitemLength > 1 ? 'Items' : 'Item'})
					</span>
				</h4>

				<div className="col-span-6 font-[700] text-[0.875rem] text-[#A63494] text-right">
					{salesData?.payableAmount}
				</div>
			</div>

			<div className="h-[1px] bg-[#CBD5E0] my-7"></div>

			<div className="grid grid-cols-12">
				<h4 className="col-span-6 font-[600] text-[#4A5568] text-[12px]">Cashier</h4>

				<div className="col-span-6 font-[700] text-[12px] text-right">{salesData?.cashier?.name}</div>
			</div>

			<div className="grid grid-cols-12 ">
				<h4 className="col-span-6 font-[600] text-[12px] text-[#4A5568]">Payment Mehtod</h4>

				<div className="col-span-6 font-[700] text-[12px] text-right">
					{salesData?.payments?.map((payment) => payment?.method).join(', ')}
				</div>
			</div>
			<div className="grid grid-cols-12">
				<h4 className="col-span-6 font-[600] text-[12px] text-[#4A5568]">Outstanding Amount</h4>

				<div className="col-span-6 font-[700] text-[12px] text-right">{salesData?.outstandingAmount ?? 0}</div>
			</div>
		</div>
	);
};

export default SalesDetailsNew;
