import { Calendar, PrinterIcon } from 'lucide-react';
import { DateRange } from 'react-day-picker';
import React from 'react';
import { cn } from '../../../../lib/utils';
import CustomDateRangePicker from '../../../../components/date-range-picker';
import { useRefresh } from '../../../../hooks/useRefresh';
import { useGetSalesReportSummary } from '../queries';

type ReportsFilterProps = {
	onDateSelect: (range: DateRange | undefined) => void;
	selectedDate: DateRange | undefined;
	downloadReport: () => void;
	salesData?: any[];
};

const ReportsFilter = ({ onDateSelect, downloadReport, selectedDate, salesData }: ReportsFilterProps) => {
	const { data } = useGetSalesReportSummary({
		filterBy: 'today',
		endDate: selectedDate?.to,
		startDate: selectedDate?.from,
	});

	const { Component: RefreshSalesReport } = useRefresh(['sales-report', 'sales-report-summary']);

	const [isDownloading, setIsDownloading] = React.useState(false);

	const promisifyDownloadReport = () => {
		return new Promise((resolve, reject) => {
			setTimeout(() => {
				resolve(downloadReport());
			}, 200);
		});
	};

	const handleDownloadReport = async () => {
		try {
			setIsDownloading(true);
			await promisifyDownloadReport();
		} catch (err) {
		} finally {
			setIsDownloading(false);
		}
	};

	return (
		<div className="flex items-center justify-between">
			<div className="flex items-center justify-content-center">
				<h1 className="text-[0.95rem] font-bold flex self-center mt-2">Daily Sales Report</h1>

				{RefreshSalesReport}
				<div className="flex items-center align-self-center h-[3vh] w-[1px] bg-[#C05621] mx-2"></div>

				<div className="flex items-center ml-3">
					<span className="text-[#4A5568] text-[0.875em] font-normal pr-3 ml-5">Date</span>
					<CustomDateRangePicker
						placeholder="Pick a date period"
						selectedRange={selectedDate}
						onRangeChange={onDateSelect}
						name="reports filter"
						icon={<Calendar size={14} />}
					/>
				</div>

				<div className="flex items-center ml-4 mt-2">
					<div className="flex items-center">
						<h3 className="text-[#4A5568] text-[0.875rem] font-[400]">Total Sale</h3>

						<div className="text-[text-[#000000] mx-2 font-[700] text-[18px] -mt-2">
							{data?.data?.salesSummary?.totalSales ?? 0}
						</div>
					</div>

					<div className="flex items-center mx-4">
						<h3 className="text-[#4A5568] text-[0.875rem] font-[400]">Sale Total</h3>

						<div className="text-[#000000] -mt-2 mx-2 font-[700] text-[18px]">
							{data?.data?.salesSummary?.totalAmount ?? 0}
						</div>
					</div>
				</div>
			</div>

			<div className="flex justify-between items-center">
				<div className="flex items-center border border-[#E2E8F0] px-2 py-1 rounded-lg mr-5">
					<PrinterIcon size={14} className="text-[#A63494] mr-2" />

					<button
						onClick={handleDownloadReport}
						disabled={isDownloading || (salesData || [])?.length <= 0}
						className={cn('font-bold text-[0.875em] text-[#1A202C]', 'disabled:opacity-70 disabled:cursor-not-allowed')}
					>
						{isDownloading ? 'Downloading' : 'Download'}
					</button>
				</div>
			</div>
		</div>
	);
};

export default ReportsFilter;
