import { useQuery } from "react-query";
import { SalesReportSummaryTransactionResponse, SalesReportTransactionResponse } from "../types";

import { axiosInstanceShoppaBackend } from "../../../../integrations/axios.config";
import { FilterBy } from "@/models";
import { formatToISOEndOfDayLocal, formatToISOStartOfDayLocal } from "../../../../utils/formatDates";

type SalesReportQueryParams = {
    searchTerm?: string;
    bookmark: string | null;
    filterBy: FilterBy | null,
    endDate?: Date | undefined;
    startDate?: Date | undefined
}

type SalesReportResponse = {
    data: {
        sales: SalesReportTransactionResponse[];
        nextPageBookmark: string | null;
        previousPageBookmark: string | null;
    };
}

export const useGetSalesReport = (params: SalesReportQueryParams) => {
    const { searchTerm, filterBy, startDate, endDate, bookmark } = params;

    const dateKey = startDate && endDate ? [`${startDate}${endDate}`] : []

    return useQuery<SalesReportResponse>(
        ['sales-report', searchTerm, bookmark, ...dateKey, filterBy],
        async () => {
            const queryParams = new URLSearchParams();
            if (searchTerm) queryParams.append('searchTerm', searchTerm);

            if (bookmark) queryParams.append('bookmark', bookmark);

            if (filterBy && (!startDate || !endDate)) {
                queryParams.append('filterBy', filterBy);
            }

            if (startDate && endDate) {
                const formattedStartDate = formatToISOStartOfDayLocal(startDate);
                queryParams.append('startDate', formattedStartDate);

                const formattedEndDate = formatToISOEndOfDayLocal(endDate);
                queryParams.append('endDate', formattedEndDate);

                queryParams.append('filterBy', '');
            }

            return await axiosInstanceShoppaBackend.get(`/sales?${queryParams.toString()}`)
        }
    );
};


type SalesReportSummaryResponse = {
    data: {
        salesSummary: SalesReportSummaryTransactionResponse;
    };
}

type SalesReportSummaryQueryParams = {
    filterBy: FilterBy | null,
    endDate?: Date | undefined;
    startDate?: Date | undefined
}

export const useGetSalesReportSummary = (params: SalesReportSummaryQueryParams) => {
    const { filterBy, endDate, startDate } = params;


    const dateKey = startDate && endDate ? `${startDate}${endDate}` : null

    return useQuery<SalesReportSummaryResponse>({
        queryKey: ['sales-report-summary', filterBy, dateKey],
        queryFn: async () => {
            const queryParams = new URLSearchParams();

            if (filterBy && (!startDate || !endDate)) queryParams.append('filterBy', filterBy);

            if (startDate && endDate) {

                queryParams.append('startDate', formatToISOStartOfDayLocal(startDate));


                const formattedEndDate = new Date(endDate).toISOString();
                queryParams.append('endDate', formatToISOEndOfDayLocal(endDate));

                queryParams.append('filterBy', '');


            }
            return await axiosInstanceShoppaBackend.get(`/report/sales/summary?${queryParams.toString()}`);
        },
    });
};
