import { UserResponseProps } from "@/types";

type Payment = {
    id: number | string;
    method: string;
    amountPaid: number;
    note: string | null;
};

export type SalesItem = {
    productId: string;
    productName: string;
    variantId: string | null;
    sellingPrice: number;
    discount: number;
    quantity: number;
    unitCost: number;
    refCartId: string;
    sku: string;
    isVoid?: boolean;
    totalAmount: number
};

export type SalesReportTransactionResponse = {
    _id: string;
    _rev: string;
    cashier: {
        name: string;
        id: string;
    };
    merchantName: string;
    assignedLocation: UserResponseProps['assignedLocations'][0];
    type: 'sales';
    status: 'processing' | 'completed' | 'void';
    createdAt: string;
    updatedAt: string;
    salesId?: string;
    invoiceId?: string;
    products: SalesItem[];
    customer: {
        name: string;
        id: string;
    };
    isVoid: boolean;
    note: string;
    payments: Payment[];
    transactionId: string;
    salesPerson: {
        name: string;
        id: string;
    };
    totalAmount: number;
    totalAmountWithTax: number;
    discountTotal: number;
    taxTotal: number;
    amountPaid: number;
    outstandingAmount: number;
    overPaidAmount: number;
    payableAmount: number;
    taxPercentage: number
};


export type SalesReportSummaryTransactionResponse = {
    totalAmount: number;
    amountPaid: number;
    outstandingAmount: number;
    totalSales: number;
    totalItemsSold: number;
}

