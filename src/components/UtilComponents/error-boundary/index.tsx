import React from "react";
import {ErrorBoundary} from "react-error-boundary";
import ErrorFallback from "./ErrorFallback";

const ErrorBoundaryProvider = ({children}: {children: React.ReactNode}) => {
  const myErrorHandler = (
    error: Error,
    info: {componentStack?: string | null | undefined}
  ) => {
    console.log(
      error,
      "error from error boundary",
      info,
      "info from error boundary"
    );
  };

  return (
    <ErrorBoundary FallbackComponent={ErrorFallback} onError={myErrorHandler}>
      {children}
    </ErrorBoundary>
  );
};

export default ErrorBoundaryProvider;
