import React from 'react';

import styles from './index.module.scss';

import { INavBar } from './../../models/index';

import { PlaceholderIconSvg } from '../svgs/PlaceholderIconSvg';
import BackIconSvg from '../svgs/BackIconSvg';

import { useNavigate } from 'react-router-dom';

import { useClickOutside } from '../../hooks/useClickOutside';
import { useCompleteCart } from '../../integrations/react_query/mutations';
import { getCurrentCart } from '../../integrations/react_query/queryFn';
import { getSessionInfo } from '../../utils/getSessionInfo';
import { logoutUser } from '../../utils/logoutUser';
import MenuIconSvg from '../svgs/MenuIconSvg';
import WeatherWidget from '../weather';

const Navbar = ({ showBar, setNavState, navState }: INavBar) => {
	const navigate = useNavigate();

	const { userInfo } = getSessionInfo();

	const userRole = userInfo?.roles[0];

	const { mutateAsync } = useCompleteCart();

	const handleNavigateBack = async () => {
		try {
			const currentCart = await getCurrentCart();

			if (currentCart?.transactionId) {
				await mutateAsync('');
			}
			navigate('/terminal');
		} catch (err) {}
	};

	const handleLogout = async () => {
		logoutUser();
	};

	const dropdownRef = React.useRef(null);

	const [showDropdown, setShowDropdown] = React.useState(false);

	useClickOutside({ ref: dropdownRef, callback: () => setShowDropdown(false) });

	return (
		<>
			<section
				className={`relative d-flex flex-column flex-md-row flex-lg-row flex-xl-row flex-xxl-row align-items-center justify-content-between ${styles.wrapper} `}
			>
				{showBar ? (
					<div className="d-flex align-items-center">
						<aside className={`mt-2 ${styles.menu}`} onClick={() => setNavState(!navState)}>
							<MenuIconSvg />
						</aside>
						<aside className={`mt-2 mx-2 ${styles.icon}`}>
							<img alt="logo" width="100%" height="100%" src="/shoppa-icon.svg" />
						</aside>
					</div>
				) : (
					<div
						onClick={() => {
							// navigate(-1)
							handleNavigateBack();
						}}
						className={styles.cursor}
					>
						<BackIconSvg />
					</div>
				)}

				<WeatherWidget />

				<div className="d-flex align-items-center">
					<div className={`${styles.grWrapper} mx-2`}>
						<aside className={`${styles.greeting}`}>
							<span>
								Hello,{' '}
								{userInfo?.profile?.firstName && userInfo?.profile?.lastName
									? `${userInfo?.profile?.firstName} ${userInfo?.profile?.lastName}`
									: userInfo?.profile?.email}
							</span>
						</aside>
						<aside className={`${styles.position}`}>
							<span>{userRole}</span>
						</aside>
					</div>

					<div
						className="position-relative"
						onClick={() => {
							setShowDropdown(!showDropdown);
						}}
					>
						<PlaceholderIconSvg width="40" height="40" viewBox="0 0 50 50" />
					</div>
				</div>

				{showDropdown && (
					<div
						ref={dropdownRef}
						className={`position-absolute shadow-sm ${styles.dropDown} px-4 mt-1 rounded`}
						style={{ right: '3em', top: '3.5em', zIndex: 100 }}
					>
						<div className={`${styles.dropDownList} py-2`} onClick={handleLogout}>
							Logout
						</div>
					</div>
				)}
			</section>
		</>
	);
};

export default React.memo(Navbar);
