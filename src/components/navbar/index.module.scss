@import '../../styles/mixins';

.wrapper {
	background: var(--main-color-extra-light);
	padding: 0.4rem 1.3rem;

	.menu {
		cursor: pointer;
	}

	.icon {
		cursor: pointer;
		@include font('<PERSON><PERSON>', 1.2em, 900);
		color: var(--white-color);
	}

	.weatherText {
		color: var(--deep-pink);
		@include font('Josef<PERSON> Sans', 0.7em, 500);
	}

	.grWrapper {
		text-align: right;
	}

	.cursor {
		cursor: pointer;
	}

	.position {
		color: var(--deeper-pink);
		@include font('<PERSON><PERSON>', 0.75em, 400);
	}
	.greeting {
		color: var(--white-color);
		@include font('<PERSON><PERSON>', 0.8em, 400);
	}

	.dropDown {
		background: var(--white-color);

		.dropDownList {
			cursor: pointer;
			transition: 0.3s;
			:hover {
				opacity: 0.7;
			}
		}
	}
}
