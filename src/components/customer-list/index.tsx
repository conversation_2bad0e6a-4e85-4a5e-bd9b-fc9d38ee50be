import React from 'react';

import useContact from './../../hooks/useContact';

import styles from './index.module.scss';

import CloseIconSvg from './../svgs/CloseIconSvg';

import { motion } from 'framer-motion';

import { customerListVariant } from './../../utils/anims/index';

import Triangle from '../resuable-ui/triangle';
import NameIndexer from '../resuable-ui/name-indexer';
import { useGetCustomer } from '../../integrations/react_query/queries';
import { EQuerykeys } from '../../models';

interface ICustomerList {
	setShow: (arg: boolean) => void;
	searchValue: string;
	debounceSearchValue: string;
}

const CustomerList = ({ setShow, debounceSearchValue }: ICustomerList) => {
	const { data: customerData, isLoading, isError } = useGetCustomer([EQuerykeys.customer]);

	const filteredCustomerData =
		!debounceSearchValue || debounceSearchValue?.length < 1
			? []
			: (customerData ?? [])?.filter((customer: any) => {
					return (
						customer?.firstName?.toLocaleLowerCase()?.includes(debounceSearchValue?.toLocaleLowerCase()) ||
						customer?.lastName?.toLocaleLowerCase()?.includes(debounceSearchValue?.toLocaleLowerCase()) ||
						customer?.email?.toLocaleLowerCase() === debounceSearchValue?.toLocaleLowerCase() ||
						customer?.phoneNumber === debounceSearchValue
					);
				});

	const listObj = useContact(filteredCustomerData ?? []);

	if (debounceSearchValue?.length < 1) return null;

	if (isLoading) {
		return <div>loading...</div>;
	}

	if (isError) {
		return <div>Something went wrong. Try again.</div>;
	}

	return (
		<section className="position-relative">
			<motion.section
				variants={customerListVariant}
				initial="hidden"
				animate="visible"
				exit="hidden"
				className={`position-relative ${styles.wrapper}`}
			>
				<motion.div
					initial={{ opacity: 0 }}
					animate={{
						opacity: 1,
						transition: { duration: 0.6, ease: 'easeIn' },
					}}
					exit={{ opacity: 0 }}
					className={`${styles.triWrapper} position-absolute w-100 d-flex justify-content-end `}
				>
					<Triangle />
				</motion.div>
				<div className={`${styles.icon} position-absolute d-flex justify-content-end`} onClick={() => setShow(false)}>
					<CloseIconSvg width="17" height="17" viewBox="0 0 25 25" />
				</div>
				{!debounceSearchValue ? (
					<div className="py-5">search for a user</div>
				) : debounceSearchValue?.length < 1 ? (
					<aside>provide more than 1 character</aside>
				) : Object.keys(listObj)?.length <= 0 ? (
					<aside className="d-flex align-items-center py-5 px-3">No user match this search</aside>
				) : (
					<NameIndexer
						{...{
							list: listObj?.length > 100 ? listObj?.slice(0, 100) : listObj,
							closeModal: () => setShow(false),
						}}
					/>
				)}
			</motion.section>
		</section>
	);
};

export default CustomerList;
