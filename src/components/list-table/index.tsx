'use client';

import React from 'react';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
} from '@tanstack/react-table';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../components/ui/table';
import {cn} from '../../lib/utils';

type GenericObject = Record<string, unknown>;

type TableProps<T extends GenericObject> = {
  data: T[];
  columns: ColumnDef<T, T>[];
  handleRowClick?: (v: T) => void;
  emptyState?: React.ReactNode;
  isCursorPointer?: boolean;
};

export default function ListTable<T extends GenericObject>({
  data,
  columns,
  handleRowClick,
  emptyState,
  isCursorPointer,
}: TableProps<T>) {
  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  return (
    <div className="px-1">
      <div className="w-full">
        <Table>
          <TableHeader className="text-[#4A5568]">
            {table.getHeaderGroups().map(headerGroup => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map(header => (
                  <TableHead
                    key={header.id}
                    colSpan={header.colSpan}
                    style={{width: `${header.getSize()}px`}}
                  >
                    {header.isPlaceholder ? null : (
                      <div className="text-left">
                        {flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                      </div>
                    )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>

          <TableBody className="capitalize">
            {table.getRowModel().rows.length > 0 ? (
              table.getRowModel().rows.map(row => (
                <TableRow
                  onClick={() => handleRowClick?.(row.original) ?? (() => {})}
                  className={cn(
                    row.getIsExpanded() && 'border-b border-black',
                    (row.getIsSelected() || row.getIsExpanded()) &&
                      'bg-[#EDF2F7]',
                    isCursorPointer && 'cursor-pointer'
                  )}
                  key={row.id}
                >
                  {row.getVisibleCells().map(cell => (
                    <TableCell key={cell.id} className="py-2">
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow className="h-[55vh] !hover:bg-transparent !bg-none !bg-transparent !hover:bg-none">
                <TableCell
                  colSpan={columns.length}
                  className="py-10 text-center text-gray-500"
                >
                  {emptyState}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
