import * as React from 'react';
import { format } from 'date-fns';
import { DateRange } from 'react-day-picker';
import { cn } from '../../lib/utils';
import { Button } from '../../components/ui/button';
import { Calendar } from '../../components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '../../components/ui/popover';
import { FieldError } from 'react-hook-form';

type CustomDateRangePickerProps = {
	name: string;
	selectedRange?: DateRange | undefined;
	onRangeChange: (range: DateRange | undefined) => void;
	icon?: React.ReactNode;
	label?: string;
	placeholder?: string;
	error?: FieldError;
	required?: boolean;
	wrapperClassName?: string;
	buttonClassName?: string;
	labelFlexSize?: string;
	fieldFlexSize?: string;
	disableBeforeDate?: Date;
	disableAfterDate?: Date;
};

const CustomDateRangePicker = ({
	name,
	selectedRange,
	onRangeChange,
	icon,
	label,
	placeholder = 'Pick a date range',
	error,
	required = false,
	wrapperClassName,
	buttonClassName,
	fieldFlexSize = 'flex-[9]',
	labelFlexSize = 'flex-[3]',
	disableBeforeDate,
	disableAfterDate = new Date(),
}: CustomDateRangePickerProps) => {
	return (
		<div className={cn('flex', wrapperClassName)}>
			{label && (
				<label htmlFor={name} className={cn('text-sm', labelFlexSize, required && 'text-red-500')}>
					{label}
				</label>
			)}
			<div className={cn(fieldFlexSize, 'bg-white')} style={{ zIndex: 1000 }}>
				<Popover>
					<PopoverTrigger>
						<Button
							id={name}
							variant="outline"
							className={cn(
								'w-full justify-start items-center text-left font-normal',
								!selectedRange && 'text-muted-foreground',
								buttonClassName
							)}
						>
							{icon && <span className="mr-1">{icon}</span>}
							{selectedRange?.from ? (
								selectedRange.to ? (
									<>
										{format(selectedRange.from, 'LLL dd, y')} - {format(selectedRange.to, 'LLL dd, y')}
									</>
								) : (
									format(selectedRange.from, 'LLL dd, y')
								)
							) : (
								<span>{placeholder}</span>
							)}
						</Button>
					</PopoverTrigger>
					<PopoverContent className="w-auto p-0">
						<Calendar
							mode="range"
							selected={selectedRange}
							onSelect={onRangeChange}
							numberOfMonths={2}
							initialFocus
							disabled={{
								before: disableBeforeDate,
								after: disableAfterDate,
							}}
						/>
					</PopoverContent>
				</Popover>
			</div>
			{error && <span className="text-sm text-red-500">{error.message}</span>}
		</div>
	);
};

export default CustomDateRangePicker;
