import { motion } from 'framer-motion';

const SyncInfo = () => {
	const syncingVariants = {
		visible: { opacity: [0.8, 1, 0.8], transition: { duration: 1.5, repeat: Infinity } },
	};

	const dotsVariants = {
		visible: { opacity: [0, 1, 0], transition: { duration: 1, repeat: Infinity, ease: 'linear' } },
	};
	return (
		<motion.div
			variants={syncingVariants}
			animate="visible"
			className="position-fixed d-flex text-dark align-items-center justify-content-center"
			style={{
				height: '100%',
				// width: 'fit-content',
				bottom: 0,
				left: '50%',
				transform: 'translateX(-50%)',
				zIndex: 100000,
				position: 'absolute',
				width: '100%',
				top: 0,
				backdropFilter: 'blur(2.5px)',
				background: 'rgb(126, 125, 125, 0.7)',
			}}
		>
			<div
				className="pt-1 pb-2 px-2 rounded text-center"
				style={{ color: 'var(--main-color-light)', background: 'white', fontFamily: 'josefin sans' }}
			>
				<span>Syncing latest updates, kindly wait.</span>
				<motion.span variants={dotsVariants} animate="visible">
					...
				</motion.span>
			</div>
		</motion.div>
	);
};

export default SyncInfo;
