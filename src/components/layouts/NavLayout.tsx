import React from 'react';

import SideMenu from './../side-menu';

import Navbar from '../navbar';

interface INavLayout {
	children: React.ReactNode;
	showBar: boolean;
}

const NavLayout = ({ children, showBar }: INavLayout) => {
	const [navState, setNavState] = React.useState(false);

	return (
		<div style={{ height: '91vh' }}>
			<Navbar {...{ navState, setNavState, showBar }} />
			<SideMenu {...{ navState, setNavState }} />
			{children}
		</div>
	);
};

export default NavLayout;
