@import '../../../styles/mixins';

.wrapper {
	margin: 3em;
	background-color: var(--white-color);
	border-radius: 20px;
	// padding: 1em 2em;
	overflow-y: auto;
	@include boxshadow();

	.title {
		margin: 1.5em 3em;
		color: var(--deep-black);
		@include font('<PERSON><PERSON> Sans', 1.1em, 500);

		@media screen and (max-width: 800px) {
			margin: 1em 1em;
		}
	}

	.cursorDisabled {
		cursor: not-allowed;
	}
}
