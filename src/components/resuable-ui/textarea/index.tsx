import React from 'react';
import { Textarea } from '../../../components/ui/textarea';
import { FieldError } from 'react-hook-form';
import { cn } from '../../../lib/utils';

interface CustomTextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
	name: string;
	label: string;
	labelFlexSize?: string;
	fieldFlexSize?: string;
	className?: string;
	error?: FieldError;
}

const CustomTextarea = ({
	name,
	label,
	error,
	required,
	labelFlexSize = 'flex-[3]',
	fieldFlexSize = 'flex-[9]',
	className,
	...props
}: CustomTextareaProps) => {
	return (
		<div className="flex">
			{label && (
				<label
					htmlFor={props?.id || name}
					className={cn(
						'block text-sm font-medium',
						required ? 'text-red-500' : 'text-[#171923]',
						labelFlexSize ? labelFlexSize : ''
					)}
				>
					{label}
				</label>
			)}

			<div className={cn('space-y-1', fieldFlexSize ? fieldFlexSize : '')}>
				<Textarea
					id={props.id || name}
					name={name}
					className={cn(
						'focus-visible:ring-[1px] focus-visible:ring-[#8341A6] focus-visible:outline-none focus-visible:border-transparent',
						error ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : 'border-[#CBD5E0]',
						className
					)}
					{...props}
				/>
				{error && <span className="text-sm text-red-500">{error?.message}</span>}
			</div>
		</div>
	);
};

export default CustomTextarea;
