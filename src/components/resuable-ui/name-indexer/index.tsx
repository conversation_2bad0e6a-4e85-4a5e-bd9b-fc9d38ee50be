import React from 'react';

import styles from './index.module.scss';

import { PlaceholderIconSvg } from './../../svgs/PlaceholderIconSvg';
import { useAttachCustomerToCart } from '../../../integrations/react_query/mutations';
import { CustomerPayload } from '../../../integrations/react_query/queryFn';

interface INameIndex {
	list: any;
	styleClass?: any;
	closeModal: () => void;
}

export const NameIndexer = ({ list, styleClass, closeModal }: INameIndex) => {
	const { mutateAsync } = useAttachCustomerToCart();

	const handleAttachCustomerToCart = ({ firstName, lastName, id, email, phoneNumber, birthday }: CustomerPayload) => {
		mutateAsync({
			firstName,
			lastName,
			id,
			email,
			phoneNumber,
			birthday,
		});

		closeModal();
	};

	return (
		<section className={`position-relative  ${styles.innerWrapper} ${styleClass && styleClass}`}>
			{Object.entries(list).map(([nameIndex, valuesIndex]: any) => {
				return (
					<React.Fragment key={nameIndex}>
						<div className={`${styles.nameIndex} text-uppercase`}>{nameIndex}</div>
						<div>
							{valuesIndex?.map((value: any, i: number) => {
								const { firstName, lastName, _id: id, email, phoneNumber } = value;
								return (
									<div
										onClick={() =>
											handleAttachCustomerToCart({
												firstName,
												lastName,
												id,
												email,
												phoneNumber,
												birthday: value.birthday,
											})
										}
										key={i}
										className={`${styles.valueIndex} d-flex align-items-center justify-content-between`}
									>
										<div className="d-flex align-items-center">
											<PlaceholderIconSvg width="35" height="35" viewBox="0 0 50 50" />
											<div className="mx-2">
												{firstName} {lastName}
											</div>
										</div>
										<div>{value?.phoneNumber}</div>
									</div>
								);
							})}
						</div>
					</React.Fragment>
				);
			})}
		</section>
	);
};

export default NameIndexer;
