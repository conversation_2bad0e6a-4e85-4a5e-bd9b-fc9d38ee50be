import React from 'react';
import { Input } from '../../../components/ui/input';
import { FieldError } from 'react-hook-form';
import { cn } from '../../../lib/utils';

interface CustomTextInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
	name: string;
	label?: string;
	labelFlexSize?: string;
	fieldFlexSize?: string;
	className?: string;
	error?: FieldError;
	wrapperClassName?: string;
}

const CustomTextInput = ({
	name,
	label,
	error,
	required,
	labelFlexSize = 'flex-[3]',
	fieldFlexSize = 'flex-[9]',
	className,
	wrapperClassName,
	...props
}: CustomTextInputProps) => {
	return (
		<div className={cn('flex', wrapperClassName)}>
			{label && (
				<label htmlFor={props.id} className={cn('text-sm', required ? 'text-red-500' : '', labelFlexSize)}>
					{label}
				</label>
			)}
			<div className={cn(fieldFlexSize)}>
				<Input
					className={cn(
						'transition-colors focus-visible:ring-[1px] focus-visible:ring-[#8341A6] focus-visible:outline-none focus-visible:border-transparent',
						error ? 'border-red-500 focus-visible:ring-red-500' : 'border-[#CBD5E0]',
						className
					)}
					{...props}
				/>
				{error && <span className="text-sm text-red-500">{error?.message}</span>}
			</div>
		</div>
	);
};

export default CustomTextInput;
