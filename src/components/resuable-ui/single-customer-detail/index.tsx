import React from 'react';

import styles from './index.module.scss';
import EditIconSvg from './../../svgs/EditIconSvg';
import CloseIconSvg from './../../svgs/CloseIconSvg';
import { useQueryClient } from 'react-query';
import { CartProps, ECart, EQuerykeys } from '../../../models';

interface ICustomerDetails {
	setEdit: (arg?: boolean) => void;
	handleClear: (arg?: boolean) => void;
	showEditIcon?: boolean;
	showCancelIcon?: boolean;
}

const SingleCustomerDetail = ({
	setEdit,
	handleClear,
	showCancelIcon = true,
	showEditIcon = true,
}: ICustomerDetails) => {
	const queryClient = useQueryClient();

	const activeCartData: CartProps | undefined = queryClient.getQueryData([EQuerykeys.cart, ECart.active]);

	const { name, phoneNumber, email } = activeCartData?.customer ?? {};

	return (
		<section className={`${styles.wrapper} flex-fill mx-4`}>
			<div className="d-flex justify-content-between align-items-center">
				{activeCartData?.customer ? (
					<>
						<div>
							<div className={styles.name}>{name}</div>
							<div>
								<div className={styles.email}>{email}</div>
								<div className={styles.phone}>{phoneNumber}</div>
							</div>
						</div>

						{showEditIcon && (
							<div className={`d-flex align-self-start ${styles.editIcon}`} onClick={() => setEdit()}>
								<EditIconSvg />
							</div>
						)}
						{showCancelIcon && (
							<div className={styles.closeIcon} onClick={() => handleClear()}>
								<CloseIconSvg width="17" height="17" viewBox="0 0 25 25" />
							</div>
						)}
					</>
				) : (
					<div className={styles.name}>Walk In</div>
				)}
			</div>
		</section>
	);
};

export default SingleCustomerDetail;
