import React from 'react';
import { RadioGroup, RadioGroupItem } from '../../../components/ui/radio-group';
import { cn } from '../../../lib/utils';
import { FieldError } from 'react-hook-form';

type Option = { label: string; value: string; disabled?: boolean };

interface CustomRadioGroupProps {
	label: string;
	options: Option[];
	value?: string;
	onChange?: (value: string) => void;
	error?: FieldError;
	labelFlexSize?: string;
	fieldFlexSize?: string;
	className?: string;
	required?: boolean;
	infoText?: string;
}

const CustomRadioGroup = ({
	label,
	options,
	value,
	onChange,
	error,
	labelFlexSize = 'flex-[3]',
	fieldFlexSize = 'flex-[9]',
	required,
	className,
	infoText,
}: CustomRadioGroupProps) => {
	return (
		<div className={cn('flex', className)}>
			{label && (
				<label
					className={cn(
						'font-medium text-[#171923] text-sm',
						labelFlexSize ? labelFlexSize : '',
						required ? 'text-red-500' : ''
					)}
				>
					{label}
				</label>
			)}
			<div className={cn(fieldFlexSize ? fieldFlexSize : '')}>
				<div className="flex items-center">
					<RadioGroup className={cn('flex items-center px-0')} value={value} onValueChange={onChange}>
						{(options || []).map((option, i) => (
							<div key={option.value} className={cn(['flex items-center space-x-2', i > 0 ? 'ml-5' : ''])}>
								<RadioGroupItem
									className="disabled:bg-slate-300 "
									disabled={option?.disabled}
									value={option.value}
									id={option.value}
								/>
								<label htmlFor={option.value} className={cn(option.disabled && 'text-[#b6b8bd]')}>
									{option.label}
								</label>
							</div>
						))}
					</RadioGroup>

					{infoText && <span className="ml-3 text-[#DD6B20] font-normal text-[0.75rem] italic">{infoText}</span>}
				</div>

				{error && <span className="block text-sm text-red-500">{error.message}</span>}
			</div>
		</div>
	);
};

export default CustomRadioGroup;
