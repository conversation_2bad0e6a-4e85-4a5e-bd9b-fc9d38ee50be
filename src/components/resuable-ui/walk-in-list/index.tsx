import React from 'react';

import PlaceholderIconSvg from '../../svgs/PlaceholderIconSvg';

import styles from './index.module.scss';
import dayjs from 'dayjs';
import { useUnParkCart } from '../../../integrations/react_query/mutations';

interface IWalkInList {
	data: Array<any>;
	setShow: (v: boolean) => void;
}

const WalkInList = ({ data, setShow }: IWalkInList) => {
	const { mutateAsync } = useUnParkCart();

	const handleUnParkCartItem = async (id: string) => {
		await mutateAsync(id);
		setShow(false);
	};

	return (
		<section className={`${styles.wrapper}`}>
			{data.map((data) => {
				return (
					<div onClick={() => handleUnParkCartItem(data?._id)} key={data?.id} className={`${styles.innerWrapper}`}>
						<div className={`d-flex justify-content-between align-items-start ${styles.walkIn}`}>
							<div>
								<PlaceholderIconSvg width="40" height="40" viewBox="0 0 50 50" />
							</div>
							<div className="flex-fill mx-3 text-left">
								<div className="">
									{data?.customer ? data?.customer?.name : 'Walk in '}{' '}
									{dayjs(data?.updatedAt, 'MMM. D, YYYY').format('MMM. D, YYYY')}
								</div>
								<div className={`${styles.reason}`}>{data?.note}</div>
							</div>
							<div>{dayjs(data?.updatedAt, 'h:mma').format('h:mm A')}</div>
						</div>
					</div>
				);
			})}
		</section>
	);
};

export default WalkInList;
