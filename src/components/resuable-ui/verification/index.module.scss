@import '../../../styles/mixins';

.marginRight {
	margin-right: 2rem;

	@media screen and (max-width: 1000px) {
		margin-right: 1rem;
	}
}

.imgBg {
	border-radius: 50%;
	width: 76px;
	height: 76px;
	background-color: var(--light-pink);
}

.text1 {
	@include font('<PERSON><PERSON>', 2em, 500);
	color: var(--deep-black);
	@media screen and (max-width: 768px) {
		@include font('<PERSON><PERSON>', 1.2em, 500);
	}
}

.text2 {
	@include font('<PERSON><PERSON>', 0.9em, 400);
	color: var(--deep-black);
	opacity: 0.6;
	@media screen and (max-width: 1000px) {
		@include font('<PERSON><PERSON>', 0.7em, 400);
	}
}

.text3 {
	@include font('<PERSON><PERSON>', 0.9em, 400);
	color: var(--main-color-extra-light);
	font-style: italic;
	@media screen and (max-width: 768px) {
		@include font('<PERSON><PERSON>', 0.7em, 400);
	}
}
