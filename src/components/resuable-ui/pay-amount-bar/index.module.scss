@import '../../../styles/mixins';

.wrapper {
	border: 1px solid var(--border-black);
	border-radius: 40px;
	padding: 0.3em 0;

	.amount {
		@include font('<PERSON><PERSON>', 2em, 400);
		@media screen and (max-width: 1000px) {
			@include font('<PERSON><PERSON>', 1.5em, 400);
		}
	}

	.icon {
		padding: 1em 1.5em;
		border-left: 1px solid var(--border-black);
		@media screen and (max-width: 1000px) {
			padding: 0.5em 1em;
		}
	}
}

.iconClose {
	outline: 0;
	border: 0;
	right: -3px;
	top: -5px;
	width: 23px;
	height: 23px;
	background-color: var(--white-color);
	border-radius: 50%;
	border: 1px solid rgba(0, 0, 0, 0.1);
	cursor: pointer;
	transition: ease-in-out 100ms;

	&:hover {
		opacity: 0.7;
	}

	&:disabled {
		cursor: not-allowed;
	}
}
