import React from 'react';
import KeyPadIconSvg from '../../svgs/KeyPadIconSvg';

import styles from './index.module.scss';

import { formatAmount } from '../../../utils/funcs';
import { useCartAnalytics } from '../../../hooks/useCartAnalytics';
import { CartProps, PaymentProps } from '../../../models';
import CloseIconSvg from '../../svgs/CloseIconSvg';
import { useRemovePayment } from '../../../integrations/react_query/mutations';

interface IPayAmountBar {
	amount?: number;
	data: CartProps & {
		payments?: PaymentProps[] | undefined;
		transactionId?: number | undefined;
	};
}

const PayAmountBar = ({ data }: IPayAmountBar) => {
	const { payableAmount } = useCartAnalytics(data?.products);

	const { mutate, isLoading } = useRemovePayment();

	const handleRemovePayment = (id: string) => {
		mutate(id);
	};

	// const groupPaymentByType = () => {
	// 	return Object.entries(
	// 		data?.payments?.reduce((acc: { [key: string]: any }, { method, amountPaid }: { [key: string]: any }) => {
	// 			if (!acc[method]) {
	// 				acc[method] = { amountPaid, occurence: 1 };
	// 			} else {
	// 				acc[method] = {
	// 					amountPaid: acc[method]?.amountPaid + amountPaid,
	// 					occurence: acc[method]?.occurence + 1,
	// 				};
	// 			}

	// 			return acc;
	// 		}, {}) ?? {}
	// 	);
	// };

	return (
		<section>
			<div className={`${styles.wrapper} d-flex align-items-center`}>
				<div className={`${styles.amount} flex-fill text-center`}>{formatAmount(payableAmount)?.toLocaleString()}</div>
				<div className={styles.icon}>
					<KeyPadIconSvg />
				</div>
			</div>

			<div
				className="postion-relative d-flex justify-content-end text-right px-4 mt-2"
				style={{ color: '#a63494', fontWeight: 600 }}
			>
				{data?.payments?.map(({ amountPaid, method: paymentMethod, id }) => {
					return (
						<div key={id} className="position-relative d-flex align-items-center justify-content-center">
							<button
								onClick={() => handleRemovePayment(id as string)}
								disabled={isLoading}
								className={`position-absolute d-flex align-items-center justify-content-center  ${styles.iconClose}`}
							>
								<CloseIconSvg width="10" height="10" viewBox="0 0 25 25" />
							</button>
							<div key={id} className="text-lower text-right mx-4">
								{paymentMethod}: {amountPaid?.toLocaleString()}
							</div>
						</div>
					);
				})}
				{/* {groupPaymentByType()?.map(([paymentMethod, { amountPaid, occurence }]: any, i: number) => {
					return (
						<div key={i} className="text-lower text-right mx-4">
							<div>x</div>
							{paymentMethod} ({occurence}): {amountPaid?.toLocaleString()}
						</div>
					);
				})} */}
			</div>
		</section>
	);
};

export default PayAmountBar;
