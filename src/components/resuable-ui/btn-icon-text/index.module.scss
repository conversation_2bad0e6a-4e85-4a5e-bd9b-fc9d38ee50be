@import '../../../styles/mixins';

.button {
	outline: 0;
	border: 0;
	background-color: transparent;
	color: var(--main-color-extra-light);
	@include font('<PERSON><PERSON>', 0.9em, 400);
	transition: 0.3s ease-in-out;

	&:disabled {
		opacity: 0.3;
		cursor: not-allowed;
	}
	&:hover {
		opacity: 0.7;
	}

	@media screen and (max-width: 1000px) {
		@include font('<PERSON><PERSON> Sans', 0.75em, 400);
	}
	//@media screen and (max-width: 900px) {
	//  @include font("<PERSON><PERSON>", 0.75em, 400);
	//}
}
