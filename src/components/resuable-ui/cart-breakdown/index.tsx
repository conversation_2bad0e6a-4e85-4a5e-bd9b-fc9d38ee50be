import React from 'react';

import { formatAmount } from '../../../utils/funcs';

import styles from './index.module.scss';

import { useQueryClient } from 'react-query';

import { ECart, EQuerykeys } from '../../../models';

import { useCartAnalytics } from '../../../hooks/useCartAnalytics';
import { getSessionInfo } from '../../../utils/getSessionInfo';

const CartBreakDown = () => {
	const queryClient = useQueryClient();

	const cartItems: any = queryClient.getQueryData([EQuerykeys.cart, ECart.active]);

	const { cartLength, totalSavings, vatAmount, payableAmount, cartSumWithoutDiscount } = useCartAnalytics(
		cartItems?.products
	);

	const { companyInfo } = getSessionInfo();

	const VAT = companyInfo?.tax ?? 0;

	return (
		<section className={`${styles.wrapper}`}>
			<div className={`${styles.items} d-flex align-items-center justify-content-between`}>
				<div>Sub total</div>
				<div>{formatAmount(cartSumWithoutDiscount)}</div>
			</div>
			<div className={`${styles.items} d-flex align-items-center justify-content-between`}>
				<div>Total Savings</div>
				<div>{formatAmount(totalSavings)}</div>
			</div>
			{VAT ? (
				<div className={`${styles.items} d-flex align-items-center justify-content-between`}>
					<div>VAT({VAT}%)</div>
					<div>{formatAmount(vatAmount)}</div>
				</div>
			) : null}
			<div className={`${styles.items} d-flex align-items-center justify-content-between`}>
				<div>
					Total{' '}
					<span>
						({cartLength} item{cartLength > 0 ? 's' : ''})
					</span>
				</div>
				<div>{formatAmount(payableAmount)}</div>
			</div>
		</section>
	);
};
export default CartBreakDown;
