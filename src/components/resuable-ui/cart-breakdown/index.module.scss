@import '../../../styles/mixins';

@mixin fontSmallScreen {
	@media screen and (max-width: 1000px) {
		@include font('<PERSON><PERSON>', 0.75em, 600);
	}
}

.wrapper {
	margin-bottom: 1em;
	margin-top: 0.5em;
	.items {
		border-top: 1px solid var(--border-black);
		padding: 0.5em;
		@include font('<PERSON><PERSON> Sans', 0.9em, 600);

		@include fontSmallScreen();
	}
	.items:nth-child(1),
	.items:nth-child(2),
	.items:nth-child(3) {
		> div:nth-child(2) {
			@include font('<PERSON><PERSON>', 1.1em, 600);
		}
	}
	.items:nth-child(4) {
		> div:nth-child(1) {
			@include font('<PERSON><PERSON> Sans', 0.9em, 600);
			> span {
				@include font('<PERSON><PERSON>', 1em, 600);
				color: var(--black-color);
			}
		}
		> div:nth-child(2) {
			@include font('<PERSON><PERSON>', 1.5em, 600);
		}
	}
}
