
import {FixedSizeList as List} from "react-window";

//interface IOptimize {
//  children: any;
//}
// import {FixedSizeGrid as Grid} from "react-window";


// import GridList from "./../sales-terminal/products/product-list/grid/grid-list/index";

// interface IOptimize {
//   children: any;
// }

const Optimize = ({component: Component, data}: any) => {
  return (
    <>
      <List
        className="bg-white"
        itemData={data}
        height={1000}
        width={"100%"}
        itemSize={35}
        itemCount={data.length}
      >
        {Component}
      </List>
      {/* <Grid
        className={`${styles.wrapper} position-relative`}
        height={400}
        width={1000}
        columnCount={3}
        rowCount={Math.ceil(data.length / 3)}
        rowHeight={250}
        columnWidth={250}
        itemData={data}
      >
        {GridList}
      </Grid> */}
    </>
  );
};

export default Optimize;
