@import "../../styles/_mixins.scss";

.wrapper {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  height: 52vh;
  overflow-y: auto;
  margin-top: 20px;

  @include scrollbars;

  .container {
    padding: 0 2rem 0 0;
    @media screen and (max-width: 1000px) {
      padding: 0 1rem 0 0;
    }
  }


  .grids{
    display: flex;
    align-items: center;
    justify-content: center;
    .gridEven{
      
    }
  }
}
