import React from 'react';

import { useNavigate } from 'react-router-dom';
import Button from '../../resuable-ui/button';
import styles from './index.module.scss';
import { formatAmount } from '../../../utils/funcs';
import MarkIconSvg from '../../svgs/MarkIconSvg';
import { useCompleteCart } from '../../../integrations/react_query/mutations';

const CompleteTransaction = ({
	data,
	totalAmount,
	totalPayment,
	handlePrintReceipt,
	isPrinting,
}: {
	data: { [key: string]: any };
	totalAmount: number;
	totalPayment: number;
	handlePrintReceipt: () => Promise<void>;
	isPrinting: boolean;
}) => {
	const { mutateAsync } = useCompleteCart();

	const navigate = useNavigate();

	const handleNextSales = async () => {
		await mutateAsync('');
		navigate('/terminal');
	};

	return (
		<section className={`d-flex flex-column align-items-stretch w-100 ${styles.wrapper}`}>
			<div className={`d-flex align-items-center justify-content-between w-100`}>
				<Button {...{ styles: `${styles.btn} py-3 py-xl-4 py-xxl-4 px-4` }}>
					<div className="d-flex justify-content-between align-items-center">
						<div>Paid</div>
						<div className={styles.value}>
							<span>&#8358; </span>
							{formatAmount(totalPayment)}
						</div>
					</div>
				</Button>

				<Button {...{ styles: `${styles.btn} py-3 py-xl-4 p-xxl-4 px-4` }}>
					<div className="d-flex justify-content-between align-items-center">
						<div>Change</div>
						<div className={styles.value}>
							<span>&#8358; </span>
							{formatAmount(+totalPayment - +totalAmount)}
						</div>
					</div>
				</Button>
			</div>

			<div className="text-center my-5">
				<div style={{ textAlign: 'center', display: 'flex', justifySelf: 'center' }}>
					<MarkIconSvg />
				</div>

				<div className={styles.tCompleted}>
					<span>Transaction Completed</span>
				</div>
				<div className={styles.tID}>
					<span>Transaction ID: {data?.transactionId}</span>
				</div>
			</div>

			<div className="text-center mt-5">
				<Button
					type="button"
					disabled={isPrinting}
					isBordered={true}
					styles="py-4 mx-3"
					style={{ width: '35%', fontSize: '1.1em' }}
					onClick={handlePrintReceipt}
				>
					{isPrinting ? 'Printing...' : 'Print Receipt'}
				</Button>

				<Button
					type="button"
					disabled={true}
					isBordered={true}
					styles="py-4 mx-3"
					style={{ width: '35%', fontSize: '1.1em' }}
				>
					Email Receipt
				</Button>
			</div>

			<div className={`${styles.nextSale} text-center mt-5`} onClick={handleNextSales}>
				Next Sale
			</div>
		</section>
	);
};

export default CompleteTransaction;
