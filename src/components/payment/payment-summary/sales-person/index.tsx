import React from 'react';
import { useAttachSalesPercon } from '../../../../integrations/react_query/mutations';
import { useGetCashiers } from '../../../../integrations/react_query/queries';
import { EQuerykeys } from '../../../../models';
import styles from './index.module.scss';
import { UserResponseProps } from '../../../../types';

const SalesPerson = () => {
	const { data: cashierData, isLoading, isError } = useGetCashiers([EQuerykeys.cashier]);

	const { mutate, isLoading: isAttachingSalesPerson } = useAttachSalesPercon();

	if (isLoading) {
		return <div>loading</div>;
	}

	if (isError) {
		return <div>An error occured</div>;
	}

	if (!cashierData || cashierData?.length <= 0) {
		return <div>No cashier found</div>;
	}

	const handleChange = async (e: React.ChangeEvent<HTMLSelectElement>) => {
		const salesPerson = cashierData?.find((c) => c?._id === e.target.value) as UserResponseProps;

		if (!salesPerson) return;

		mutate({
			id: salesPerson?._id,
			firstName: salesPerson?.profile?.firstName,
			lastName: salesPerson?.profile?.lastName,
		});
	};

	return (
		<section className={styles.wrapper}>
			<select onChange={handleChange} disabled={isAttachingSalesPerson}>
				<option>Select sales person</option>
				{cashierData?.map(({ profile: { firstName, lastName }, _id }) => {
					return (
						<option key={_id} value={_id}>
							{firstName} {lastName}
						</option>
					);
				})}
			</select>
		</section>
	);
};

export default SalesPerson;
