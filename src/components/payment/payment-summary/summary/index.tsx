import React from 'react';

import CartSummary from '../../../resuable-ui/cart-summary';

import styles from './index.module.scss';
import { CartProps, PaymentProps } from '../../../../models';

interface ISummary {
	data: CartProps & { payments?: PaymentProps[]; transactionId?: number };
}

const Summary = ({ data }: ISummary) => {
	return (
		<>
			<div className={styles.summary}>
				<span>Summary</span>
			</div>
			<section className={`${styles.wrapper}`}>
				{(data?.products ?? []).map(({ productName, sellingPrice, productId, quantity }) => (
					<CartSummary
						{...{
							name: productName,
							amount: sellingPrice ?? 0,
							quantity: quantity,
							id: productId,
						}}
					/>
				))}
			</section>
		</>
	);
};

export default Summary;
