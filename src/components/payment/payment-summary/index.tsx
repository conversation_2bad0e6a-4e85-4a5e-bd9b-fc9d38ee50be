import React from 'react';

import { CustomerDetail } from './customer-detail';

import styles from './index.module.scss';

import CartBreakDown from '../../resuable-ui/cart-breakdown';
import Summary from './summary';

import { useGetCart } from '../../../integrations/react_query/queries';
import { ECart, EQuerykeys } from '../../../models';
// import SalesPerson from './sales-person';

const PaymentSummary = () => {
	const { isLoading, isError, data } = useGetCart([EQuerykeys.cart, ECart.active]);

	return (
		<section className={`${styles.wrapper} col-12 col-md-5 col-lg-5 col-xl-5 col-xxl-5`}>
			<CustomerDetail showCancelIcon={false} showEditIcon={false} />

			{isLoading ? (
				<div>Loading...</div>
			) : isError ? (
				<div>error</div>
			) : !data || (data?.products || []).length <= 0 ? (
				<div>No checkout cart found</div>
			) : (
				<Summary data={data} />
			)}

			<CartBreakDown />

			{/* <SalesPerson /> */}
		</section>
	);
};

export default PaymentSummary;
