import React from 'react';

import styles from './index.module.scss';

import SingleCustomerDetail from '../../../resuable-ui/single-customer-detail';

import PlaceholderIconSvg from '../../../svgs/PlaceholderIconSvg';

export const CustomerDetail = ({
	showCancelIcon,
	showEditIcon,
}: {
	showCancelIcon?: boolean;
	showEditIcon?: boolean;
}) => {
	return (
		<div className={`d-flex align-items-center justify-content-center ${styles.wrapper}`}>
			<div>
				<PlaceholderIconSvg width="40" height="40" viewBox="0 0 50 50" />
			</div>
			<SingleCustomerDetail
				showCancelIcon={showCancelIcon}
				showEditIcon={showEditIcon}
				{...{ handleClear: () => {}, setEdit: () => {} }}
			/>
		</div>
	);
};
