'use client';

import React from 'react';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '../../../../ui/button';
import { Label } from '../../../../ui/label';
import { RadioGroup, RadioGroupItem } from '../../../../ui/radio-group';
import CustomTextInput from '../../../../resuable-ui/text-input';
import CustomTextarea from '../../../../resuable-ui/textarea';
import { Dialog, DialogContent, DialogTitle } from '../../../../ui/dialog';
import { useCartAnalytics } from '../../../../../hooks/useCartAnalytics';
import { useMarkCartWithTransactionID, usePayment } from '../../../../../integrations/react_query/mutations';
import { useGetCart, useGetPaymentTypes } from '../../../../../integrations/react_query/queries';
import { useCompleteSales } from '../../../../../hooks/useCompleteSales';
import { EQuerykeys, ECart } from '../../../../../models/index';
import { handlePayment, calculateBalanceDue } from '../utils/paymentUtils';
import { PaymentTypes } from '../../../../../types/index';

type PaymentMethod = {
	label: string;
	id: string;
	value: string;
};

interface CashPaymentConfirmationProps {
	title?: string;
	methods: PaymentMethod[];
	defaultAmount: string | number;
	onConfirm: (data: PaymentConfirmationForm) => void;
	onClear?: () => void;
	showConfirmationModal: boolean;
	setShowConfirmationModal: (show: boolean) => void;
	paymentType: string;
}

const PaymentConfirmationSchema = z.object({
	method: z.string().nonempty('Payment method is required'),
	amount: z.coerce.number().min(1, 'Amount must be at least 1'),
	note: z.string().max(100, 'Note must be at most 100 characters').optional(),
});

type PaymentConfirmationForm = z.infer<typeof PaymentConfirmationSchema>;

const CashCreditPaymentConfirmation: React.FC<CashPaymentConfirmationProps> = ({
	title = 'Card Payment Confirmation',
	methods,
	defaultAmount,
	onConfirm,
	onClear,
	showConfirmationModal,
	setShowConfirmationModal,
	paymentType,
}) => {
	console.log({ paymentType });

	const { control, handleSubmit, watch, setValue } = useForm<PaymentConfirmationForm>({
		resolver: zodResolver(PaymentConfirmationSchema),
		defaultValues: {
			method: '',
			amount: '',
			note: '',
		},
	});

	const derivedPaymentType =
		paymentType === PaymentTypes.card ? 'pos' : paymentType === PaymentTypes.transfer ? 'bank' : '';

	const { data: paymentMethods, isLoading: isLoadingPaymentMethods } = useGetPaymentTypes([
		EQuerykeys.payment_types,
		derivedPaymentType,
	]);

	React.useEffect(() => {
		if (!paymentMethods) return;
		setValue('method', paymentMethods?.[0]?._id);
	}, [isLoadingPaymentMethods, paymentMethods]);

	const { data: cartItems, isLoading } = useGetCart([EQuerykeys.cart, ECart.active]);
	const { completeTransaction, transactionId } = useCompleteSales();
	const { mutateAsync: markCartWithTransactionID } = useMarkCartWithTransactionID();
	const { mutateAsync: makePayment } = usePayment();

	const { payableAmount } = useCartAnalytics(cartItems?.products);

	const onSubmit = async (data: PaymentConfirmationForm) => {
		if (!cartItems) return;

		const paymentProvider = paymentMethods?.find((method: any) => method?._id === data?.method) ?? {};

		await handlePayment({
			amountPaid: data.amount,
			note: data.note,
			paymentType: paymentType as PaymentTypes,
			paymentsHistory: cartItems.payments,
			payableAmount: +payableAmount,
			transactionId,
			onPayment: makePayment as unknown as any,
			markCartWithTransactionID,
			completeTransaction,
			onSuccess: () => {
				setShowConfirmationModal(false);
			},
			paymentProvider,
		});
	};

	const noteValue = watch('note') || '';
	const amountValue = watch('amount') || 0;

	const balanceDue = React.useMemo(() => {
		if (!cartItems) return 0;
		return calculateBalanceDue(+payableAmount, cartItems.payments, Number(amountValue));
	}, [cartItems, amountValue]);

	if (isLoading) {
		return <div>Loading...</div>;
	}
	if (!cartItems) {
		return <div>No available data</div>;
	}

	return (
		<Dialog open={showConfirmationModal} onOpenChange={(v) => setShowConfirmationModal(v)}>
			<DialogContent className="!min-w-[750px] bg-white px-3 gap-0 sm:w-full">
				<DialogTitle className="flex items-center">
					<h3 className="text-lg uppercase text-[20px] font-[700]">{title}</h3>
				</DialogTitle>

				<form onSubmit={handleSubmit(onSubmit)}>
					<div className="grid md:grid-cols-2 gap-6 mt-3">
						<div>
							<div className="flex items-center justify-between mb-2 text-[#353535] font-[600] text-[12px]">
								<Label htmlFor="amount">
									Balance due -{' '}
									<span className={balanceDue < 0 ? 'text-red-500' : balanceDue === 0 ? 'text-green-600' : ''}>
										₦{Math.abs(balanceDue).toLocaleString(undefined, { minimumFractionDigits: 2 })}
										{balanceDue < 0 && ' (Overpaid)'}
									</span>
								</Label>
							</div>

							<Controller
								name="amount"
								control={control}
								render={({ field, fieldState }) => (
									<CustomTextInput
										id="amount"
										{...field}
										onChange={(e) => field.onChange(e.target.value)}
										className="mt-1 h-[45px]"
										placeholder="Enter amount paid"
										error={fieldState.error}
									/>
								)}
							/>
						</div>

						<div>
							<div className="flex items-center justify-between mb-2 text-[#353535] font-[600] text-[12px]">
								<Label htmlFor="amount">Total - ₦{Number(defaultAmount).toLocaleString()}</Label>
							</div>

							<Button
								disabled={balanceDue <= 0}
								onClick={() => {
									if (balanceDue > 0) {
										setValue('amount', balanceDue + +amountValue);
									}
								}}
								type="button"
								className="w-full font-semibold text-xl text-center bg-[#A63494] text-white py-4 rounded"
							>
								₦ {Number(balanceDue + +amountValue).toLocaleString(undefined, { minimumFractionDigits: 2 })}
							</Button>
						</div>
					</div>

					<div className="mt-4">
						<Label className="mb-2" htmlFor="note">
							Note (Optional)
						</Label>
						<Controller
							name="note"
							control={control}
							render={({ field, fieldState }) => (
								<CustomTextarea
									label=""
									id="note"
									{...field}
									placeholder="Write a note..."
									maxLength={100}
									error={fieldState.error}
									rows={5}
								/>
							)}
						/>
						<div className="text-right text-xs text-muted-foreground">{noteValue.length}/100</div>
					</div>

					<div className="grid md:grid-cols-2 gap-6 mt-6">
						<Button
							className="max-w-fit border border-[#E2E8F0] bg-[#FEFBFF] py-4 text-[#718096]"
							type="button"
							variant="outline"
							onClick={onClear}
						>
							Clear
						</Button>

						<Button className="flex-1 bg-[#A63494] py-4 font-semibold text-xl text-center text-white" type="submit">
							OK
						</Button>
					</div>
				</form>
			</DialogContent>
		</Dialog>
	);
};

export default CashCreditPaymentConfirmation;
