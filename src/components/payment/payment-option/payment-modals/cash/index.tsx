import React from 'react';

import PaymentModalWrapper from './../../../../resuable-ui/generic-modal-wrapper';
import TextArea from './../../../../resuable-ui/text-area';
import { InputField } from './../../../../resuable-ui/input/index';
import Button from '../../../../resuable-ui/button';

import * as Yup from 'yup';
import { Formik, Form, FormikHelpers, FormikProps } from 'formik';

import { formatAmount } from '../../../../../utils/funcs';

import styles from './index.module.scss';
import { ECart, EQuerykeys, PaymentProps } from '../../../../../models';
import { useCartAnalytics } from '../../../../../hooks/useCartAnalytics';
import { useMarkCartWithTransactionID, usePayment } from '../../../../../integrations/react_query/mutations';
import { useGetCart } from '../../../../../integrations/react_query/queries';
import { useCompleteSales } from '../../../../../hooks/useCompleteSales';
import Modal from '../../../../resuable-ui/modal';
import { PaymentTypes } from '../../../../../types';
import { triggerToast } from '../../../../../toast';

const validationSchema = Yup.object().shape({
	amount: Yup.number()
		.typeError('amount paid must be in numbers')
		.required('please enter amount paid')
		.test({
			test: (v) => (v || 0) > 0,
			message: 'amount paid must be greater than zero',
		}),
});

type TPayment = {
	amount: number;
	note: string;
};

const initialValues: TPayment = {
	amount: '' as unknown as number,
	note: '',
};

interface ICashModalContent {
	title: string;
	setShow: (arg: boolean) => void;
	paymentType: string;
}

const CashModalContent = ({ title, paymentType, setShow }: ICashModalContent) => {
	const { isLoading, data: cartItems } = useGetCart([EQuerykeys.cart, ECart.active]);

	const { payableAmount } = useCartAnalytics(cartItems?.products);
	const fixedPayableAmount = +payableAmount;

	const { completeTransaction, transactionId } = useCompleteSales();

	const [showConfirmationModal, setShowConfirmationModal] = React.useState(false);

	const { mutateAsync: markCartWithTransactionID } = useMarkCartWithTransactionID();

	const { mutateAsync } = usePayment();

	const onSubmit = async (values: TPayment, actions: FormikHelpers<TPayment>) => {
		//TODO: confirm that the amount is greater than what they want to pay for

		const amountAsNumber = +values.amount;

		let totalAmountPaymentHistory = getTotalAmountFromPaymentHistory(cartItems?.payments);

		if (isNaN(values.amount) || amountAsNumber < 0) return;

		if (
			+(+amountAsNumber.toFixed(2) > +fixedPayableAmount) ||
			+(totalAmountPaymentHistory + +values.amount).toFixed(2) > +fixedPayableAmount
		) {
			if (paymentType === PaymentTypes.credit) {
				triggerToast({
					type: 'error',
					message: 'Illegal credit amount, try again!',
					position: 'bottom-right',
				});

				return;
			}

			setShowConfirmationModal(true);
			return;
		}

		// await mutateAsync({
		// 	amountPaid: amountAsNumber,
		// 	note: values.note || null,
		// 	method: paymentType as PaymentTypes,
		// });
		setShow(false);
		actions.resetForm?.();

		if (amountAsNumber >= +fixedPayableAmount || totalAmountPaymentHistory + amountAsNumber >= +fixedPayableAmount) {
			await markCartWithTransactionID(transactionId);
			await completeTransaction();
		}
	};

	const handleCompleteSales = async ({ values, resetForm }: FormikProps<TPayment>) => {
		const amountAsNumber = +values.amount;

		let totalAmountPaymentHistory = getTotalAmountFromPaymentHistory(cartItems?.payments);

		// await mutateAsync({
		// 	amountPaid: amountAsNumber,
		// 	note: values.note || null,
		// 	method: paymentType as PaymentTypes,
		// });

		setShow(false);
		resetForm?.();

		if (amountAsNumber >= fixedPayableAmount || totalAmountPaymentHistory + amountAsNumber >= fixedPayableAmount) {
			await markCartWithTransactionID(transactionId);
			await completeTransaction();
		}
	};

	const getTotalAmountFromPaymentHistory = (paymentHistory: PaymentProps[] | undefined) => {
		if (!paymentHistory) return 0;

		return (paymentHistory ?? []).reduce((acc, { amountPaid }) => acc + amountPaid, 0);
	};

	const calculateBalanceDueFromPaymentHistory = (totalAmount: number, inputAmount: number) => {
		let totalAmountPaymentHistory = getTotalAmountFromPaymentHistory(cartItems?.payments);

		if (!totalAmountPaymentHistory && !inputAmount) return formatAmount(totalAmount);

		const r = totalAmount - ((totalAmountPaymentHistory || 0) + (+inputAmount || 0));

		if (totalAmount === +(totalAmountPaymentHistory + +inputAmount)?.toFixed(2))
			return <span className="text-success">0</span>;

		if (+(totalAmountPaymentHistory + +inputAmount).toFixed(2) > totalAmount)
			return <span className="text-danger">+{formatAmount(Math.abs(r))}</span>;

		return formatAmount(r);
	};

	const calculateBalanceDueFromPaymentHistoryRaw = (totalAmount: number, inputAmount: number) => {
		let totalAmountPaymentHistory = getTotalAmountFromPaymentHistory(cartItems?.payments);

		if (!totalAmountPaymentHistory && !inputAmount) return totalAmount;

		const r = totalAmount - ((totalAmountPaymentHistory || 0) + (+inputAmount || 0));

		if (totalAmount === totalAmountPaymentHistory + +inputAmount) return 0;

		if (totalAmountPaymentHistory + +inputAmount > totalAmount) return formatAmount(Math.abs(r));

		return formatAmount(+r);
	};

	// const calculateBalanceDue = (totalAmount: number, inputAmount: number) => {
	// 	if (!inputAmount) return totalAmount;

	// 	if (!totalAmount) return 'invalid op';

	// 	if (totalAmount?.toLocaleString() === inputAmount?.toLocaleString()) return 'no outstanding';

	// 	if (inputAmount > totalAmount) return `overpaid`;

	// 	return totalAmount - inputAmount;
	// };

	if (isLoading) {
		return <div>"loading..."</div>;
	}
	if (!cartItems) {
		return <div>"no available data"</div>;
	}

	return (
		<>
			<PaymentModalWrapper {...{ title }}>
				<Formik {...{ initialValues, validationSchema, onSubmit, enableReinitialize: true }}>
					{(props: FormikProps<TPayment>) => {
						const calculatedBalanceDue = calculateBalanceDueFromPaymentHistory(fixedPayableAmount, props.values.amount);

						const calculatedBalanceDueRaw = calculateBalanceDueFromPaymentHistoryRaw(
							fixedPayableAmount,
							props.values.amount
						);

						return (
							<>
								<Form className="mt-4">
									<div className="d-flex justify-content-between">
										<div className={`${styles.balance} flex-fill`}>
											Balance due - <span>{calculatedBalanceDue}</span>
										</div>
										<div className="text-start flex-fill" style={{ fontWeight: 600 }}>
											Total: {formatAmount(fixedPayableAmount)}
										</div>
									</div>
									<div className="d-flex align-items-start justify-content-between">
										<div className="flex-fill">
											<InputField
												onBlur={props.handleBlur}
												id="amount"
												onChange={props.handleChange}
												type="text"
												value={props.values.amount}
												error={props.errors.amount}
												touched={props.touched.amount}
												placeholder="Enter amount paid"
												name="amount"
												showIcon={true}
											/>
										</div>

										<div className={`${styles.btnMargin} w-50`}>
											<Button
												onClick={() => {
													props.setFieldValue('amount', calculatedBalanceDue?.toLocaleString()?.replaceAll(',', ''));
												}}
												type="button"
												disabled={+calculatedBalanceDueRaw <= 0}
												styles={`${styles.btnPadding} w-100`}
											>
												&#8358; &nbsp;{calculatedBalanceDue}
											</Button>
										</div>
									</div>

									<div className="my-3">
										<TextArea
											onBlur={props.handleBlur}
											onChange={props.handleChange}
											value={props.values.note}
											className={styles.textArea}
											label="Note (optional)"
											name="note"
											placeholder="Type in any note here you want attached to the sale..."
										/>
									</div>

									<div className="w-100 text-center mt-5 mb-3">
										<Button
											type="button"
											disabled={false}
											isBordered={true}
											styles="py-4 mx-3"
											style={{ width: '45%' }}
											onClick={() => props.handleReset()}
										>
											Clear
										</Button>
										<Button type="submit" disabled={false} style={{ width: '45%' }} styles="py-4">
											Ok
										</Button>
									</div>
								</Form>
								<Modal
									{...{
										show: showConfirmationModal,
										setShow: setShowConfirmationModal,
										columnLayout: 'col-9 col-md-6 col-lg-4 col-xl-4 col-xxl-4',
									}}
								>
									<div className="bg-white py-5 mt-3">
										<div className={`${styles.infoText} px-5 text-center`}>
											The amount for the cart items has been overpaid. Click "Yes" to proceed if you confirm this.{' '}
											<p className="font-weight-bold mt-2">Note: The sale will be recorded automatically.</p>
										</div>
										<div className="w-100 text-center mt-4">
											<Button
												type="button"
												disabled={false}
												isBordered={true}
												styles="py-4"
												style={{ width: '35%', marginRight: '10px' }}
												onClick={() => setShowConfirmationModal(false)}
											>
												No
											</Button>
											<Button
												type="button"
												disabled={false}
												style={{ width: '35%' }}
												styles="py-4"
												onClick={() => {
													handleCompleteSales(props);
													setShowConfirmationModal(false);
												}}
											>
												Yes
											</Button>
										</div>
									</div>
								</Modal>
							</>
						);
					}}
				</Formik>
			</PaymentModalWrapper>
		</>
	);
};

export default CashModalContent;
