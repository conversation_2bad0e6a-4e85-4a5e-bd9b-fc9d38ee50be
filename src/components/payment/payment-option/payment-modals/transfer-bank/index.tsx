import { ECart, EQuerykeys } from '../../../../../models';
import { useCartAnalytics } from '../../../../../hooks/useCartAnalytics';
import { useGetCart } from '../../../../../integrations/react_query/queries';

import PaymentConfirmation from '../payment-confirmation/index';

interface TransferBankModalContentProps {
	title: string;
	setShow: (arg: boolean) => void;
	show: boolean;
	paymentType: string;
}

const TransferBankModalContent = ({
	title,
	paymentType,
	setShow,
	show: showPaymentModal,
}: TransferBankModalContentProps) => {
	const { isLoading, data: cartItems } = useGetCart([EQuerykeys.cart, ECart.active]);

	const { payableAmount } = useCartAnalytics(cartItems?.products);

	if (isLoading) {
		return <div>Loading...</div>;
	}
	if (!cartItems) {
		return <div>No available data</div>;
	}

	return (
		<>
			<PaymentConfirmation
				paymentType={paymentType}
				showConfirmationModal={showPaymentModal}
				setShowConfirmationModal={setShow}
				title={title}
				methods={[]}
				defaultAmount={payableAmount}
				onConfirm={(data) => console.log('Payment Confirmed', data)}
				onClear={() => console.log('Cleared')}
			/>
		</>
	);
};

export default TransferBankModalContent;
