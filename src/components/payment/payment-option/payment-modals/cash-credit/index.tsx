import { ECart, EQuerykeys } from '../../../../../models';
import { useCartAnalytics } from '../../../../../hooks/useCartAnalytics';
import { useGetCart } from '../../../../../integrations/react_query/queries';

import CashCreditPaymentConfirmation from '../cash-payment-confirmation/index';

interface CashCreditModalContentProps {
	title: string;
	setShow: (arg: boolean) => void;
	show: boolean;
	paymentType: string;
}

const CashCreditModalContent = ({
	title,
	paymentType,
	setShow,
	show: showPaymentModal,
}: CashCreditModalContentProps) => {
	const { isLoading, data: cartItems } = useGetCart([EQuerykeys.cart, ECart.active]);

	const { payableAmount } = useCartAnalytics(cartItems?.products);

	if (isLoading) {
		return <div>Loading...</div>;
	}
	if (!cartItems) {
		return <div>No available data</div>;
	}

	return (
		<>
			<CashCreditPaymentConfirmation
				paymentType={paymentType}
				showConfirmationModal={showPaymentModal}
				setShowConfirmationModal={setShow}
				title={title}
				methods={[]}
				defaultAmount={payableAmount}
				onConfirm={(data) => console.log('Payment Confirmed', data)}
				onClear={() => console.log('Cleared')}
			/>
		</>
	);
};

export default CashCreditModalContent;
