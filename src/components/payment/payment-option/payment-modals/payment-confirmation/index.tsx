'use client';

import React from 'react';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '../../../../ui/button';
import { Label } from '../../../../ui/label';
import { RadioGroup, RadioGroupItem } from '../../../../ui/radio-group';
import CustomTextInput from '../../../../../components/resuable-ui/text-input';
import CustomTextarea from '../../../../../components/resuable-ui/textarea';
import { Dialog, DialogContent, DialogTitle } from '../../../../../components/ui/dialog';
import { useCartAnalytics } from '../../../../../hooks/useCartAnalytics';
import { useMarkCartWithTransactionID, usePayment } from '../../../../../integrations/react_query/mutations';
import { useGetCart, useGetPaymentTypes } from '../../../../../integrations/react_query/queries';
import { useCompleteSales } from '../../../../../hooks/useCompleteSales';
import { EQuerykeys, ECart } from '../../../../../models/index';
import { handlePayment, calculateBalanceDue } from '../utils/paymentUtils';
import { PaymentTypes } from '../../../../../types/index';

type PaymentMethod = {
	label: string;
	id: string;
	value: string;
};

interface PaymentConfirmationProps {
	title?: string;
	methods: PaymentMethod[];
	defaultAmount: string | number;
	onConfirm: (data: PaymentConfirmationForm) => void;
	onClear?: () => void;
	showConfirmationModal: boolean;
	setShowConfirmationModal: (show: boolean) => void;
	paymentType: string;
}

const PaymentConfirmationSchema = z.object({
	method: z.string().nonempty('Payment method is required'),
	amount: z.coerce.number().min(1, 'Amount must be at least 1'),
	note: z.string().max(100, 'Note must be at most 100 characters').optional(),
});

type PaymentConfirmationForm = z.infer<typeof PaymentConfirmationSchema>;

const PaymentConfirmation: React.FC<PaymentConfirmationProps> = ({
	title = 'Card Payment Confirmation',
	methods,
	defaultAmount,
	onConfirm,
	onClear,
	showConfirmationModal,
	setShowConfirmationModal,
	paymentType,
}) => {
	const { control, handleSubmit, watch, setValue } = useForm<PaymentConfirmationForm>({
		resolver: zodResolver(PaymentConfirmationSchema),
		defaultValues: {
			method: '',
			amount: '',
			note: '',
		},
	});

	const derivedPaymentType =
		paymentType === PaymentTypes.card ? 'pos' : paymentType === PaymentTypes.transfer ? 'bank' : '';

	const { data: paymentMethods, isLoading: isLoadingPaymentMethods } = useGetPaymentTypes([
		EQuerykeys.payment_types,
		derivedPaymentType,
	]);

	React.useEffect(() => {
		if (!paymentMethods) return;
		setValue('method', paymentMethods?.[0]?._id);
	}, [isLoadingPaymentMethods, paymentMethods]);

	console.log({ paymentMethods });

	const composePaymentMethods =
		paymentMethods
			?.map((method: any) => ({
				label: method?.bank?.name || method?.terminalName,
				terminalIdentifier: method?.terminalIdentifier,
				accountNumber: method?.accountNumber,
				accountName: method?.accountName,
				provider: method?.provider?.name,
				id: method._id,
				value: method._id,
				type: method?.paymentType,
			}))
			?.filter((d: any) => d.type === derivedPaymentType) || [];

	const { data: cartItems } = useGetCart([EQuerykeys.cart, ECart.active]);
	const { completeTransaction, transactionId } = useCompleteSales();
	const { mutateAsync: markCartWithTransactionID } = useMarkCartWithTransactionID();
	const { mutateAsync: makePayment } = usePayment();

	const { payableAmount } = useCartAnalytics(cartItems?.products);

	const onSubmit = async (data: PaymentConfirmationForm) => {
		if (!cartItems) return;

		const paymentProvider = paymentMethods?.find((method: any) => method?._id === data?.method) ?? {};

		await handlePayment({
			amountPaid: data.amount,
			note: data.note,
			paymentType: paymentType as PaymentTypes,
			paymentsHistory: cartItems.payments,
			payableAmount: +payableAmount,
			transactionId,
			onPayment: makePayment as unknown as any,
			markCartWithTransactionID,
			completeTransaction,
			onSuccess: () => {
				setShowConfirmationModal(false);
			},
			paymentProvider,
		});
	};

	const noteValue = watch('note') || '';
	const amountValue = watch('amount') || 0;

	const balanceDue = React.useMemo(() => {
		if (!cartItems) return 0;
		return calculateBalanceDue(+payableAmount, cartItems.payments, Number(amountValue));
	}, [cartItems, amountValue]);

	return (
		<Dialog open={showConfirmationModal} onOpenChange={(v) => setShowConfirmationModal(v)}>
			<DialogContent className="!min-w-[850px] bg-white px-3 gap-0 sm:w-full">
				<DialogTitle className="flex items-center">
					<h3 className="text-lg uppercase text-[20px] font-[700]">{title}</h3>
				</DialogTitle>

				<form onSubmit={handleSubmit(onSubmit)}>
					<div className="grid md:grid-cols-2 gap-10 mt-3">
						<div className="space-y-4 max-h-[50vh] overflow-y-auto">
							<Label className="text-[#718096] text-[12px]">Select Payment Method</Label>
							<Controller
								name="method"
								control={control}
								render={({ field, fieldState }) => (
									<>
										<RadioGroup value={field.value} onValueChange={field.onChange}>
											{composePaymentMethods?.map((method: any) => (
												<div
													key={method.value}
													className={`flex items-center justify-between p-2 border rounded-md ${
														field.value === method.value ? 'border-primary' : 'border-muted'
													}`}
												>
													<div className="space-y-1">
														<div className="text-[14px] font-[700]">{method.label}</div>
														{method?.terminalIdentifier && (
															<div className="text-[13px] font-[400] text-muted-foreground">
																{method?.terminalIdentifier}
															</div>
														)}

														{method?.accountName && method?.accountNumber && (
															<div className="flex items-center gap-x-3 text-[13px] font-[400]">
																<div>{method?.accountName}</div>
																<div className="w-[1px] h-[10px] bg-[#DD6B20]"></div>
																<div>{method?.accountNumber}</div>
															</div>
														)}
													</div>
													<RadioGroupItem value={method.value} />
												</div>
											))}
										</RadioGroup>
										{fieldState.error && <p className="text-red-500 text-sm">{fieldState.error.message}</p>}
									</>
								)}
							/>
						</div>

						<div className="space-y-4">
							<div>
								<div className="flex items-center justify-between mb-2 text-[#353535] font-[600] text-[12px]">
									<Label htmlFor="amount">
										Balance due -{' '}
										<span className={balanceDue < 0 ? 'text-red-500' : balanceDue === 0 ? 'text-green-600' : ''}>
											₦{Math.abs(balanceDue).toLocaleString(undefined, { minimumFractionDigits: 2 })}
											{balanceDue < 0 && ' (Overpaid)'}
										</span>
									</Label>

									<Label htmlFor="amount">Total - ₦{Number(defaultAmount).toLocaleString()}</Label>
								</div>

								<Controller
									name="amount"
									control={control}
									render={({ field, fieldState }) => (
										<CustomTextInput
											id="amount"
											{...field}
											onChange={(e) => field.onChange(e.target.value)}
											className="mt-1 h-[45px]"
											placeholder="Enter amount paid"
											error={fieldState.error}
										/>
									)}
								/>

								<Button
									disabled={balanceDue <= 0}
									onClick={() => {
										if (balanceDue > 0) {
											setValue('amount', balanceDue + +amountValue);
										}
									}}
									type="button"
									className="w-full mt-4 font-semibold text-xl text-center bg-[#A63494] text-white py-4 rounded"
								>
									₦ {Number(balanceDue + +amountValue).toLocaleString(undefined, { minimumFractionDigits: 2 })}
								</Button>
							</div>

							<div className="mt-5">
								<Label className="mb-2" htmlFor="note">
									Note (Optional)
								</Label>
								<Controller
									name="note"
									control={control}
									render={({ field, fieldState }) => (
										<CustomTextarea
											label=""
											id="note"
											{...field}
											placeholder="Write a note..."
											maxLength={100}
											error={fieldState.error}
											rows={3}
										/>
									)}
								/>
								<div className="text-right text-xs text-muted-foreground">{noteValue.length}/100</div>
							</div>
						</div>
					</div>

					<div className="grid md:grid-cols-2 gap-6 mt-6">
						<Button
							className="max-w-fit border border-[#E2E8F0] bg-[#FEFBFF] py-4 text-[#718096]"
							type="button"
							variant="outline"
							onClick={onClear}
						>
							Clear
						</Button>

						<Button className="flex-1 bg-[#A63494] py-4 font-semibold text-xl text-center text-white" type="submit">
							OK
						</Button>
					</div>
				</form>
			</DialogContent>
		</Dialog>
	);
};

export default PaymentConfirmation;
