import { triggerToast } from './../../../../../toast';
import { PaymentProps } from '../../../../../models';
import { PaymentTypes } from '../../../../../types';

interface HandlePaymentParams {
	amountPaid: number;
	note?: string;
	paymentType: PaymentTypes;
	paymentsHistory: PaymentProps[] | undefined;
	payableAmount: number;
	transactionId: string;
	onPayment: (data: {
		amountPaid: number;
		note?: string | null;
		method: PaymentTypes;
		paymentProvider?: Record<string, any>;
	}) => Promise<any>;
	markCartWithTransactionID: (id: string) => Promise<any>;
	completeTransaction: () => Promise<any>;
	onSuccess?: () => void;
	paymentProvider?: Record<string, any>;
}

export const getTotalAmountFromPaymentHistory = (paymentHistory: PaymentProps[] | undefined): number => {
	if (!paymentHistory) return 0;
	return paymentHistory.reduce((acc, { amountPaid }) => acc + amountPaid, 0);
};

export const handlePayment = async ({
	amountPaid,
	note,
	paymentType,
	paymentsHistory,
	payableAmount,
	transactionId,
	onPayment,
	markCartWithTransactionID,
	completeTransaction,
	onSuccess,
	paymentProvider,
}: HandlePaymentParams) => {
	const totalHistory = getTotalAmountFromPaymentHistory(paymentsHistory);
	const totalAfterThis = totalHistory + amountPaid;

	if (isNaN(amountPaid) || amountPaid <= 0) {
		return triggerToast({
			type: 'error',
			message: 'Invalid payment amount.',
			position: 'bottom-right',
		});
	}

	const isOverPayment = totalAfterThis > payableAmount;

	if (isOverPayment && paymentType === PaymentTypes.credit) {
		return triggerToast({
			type: 'error',
			message: 'Illegal credit amount, try again!',
			position: 'bottom-right',
		});
	}

	await onPayment({
		amountPaid,
		note: note || null,
		method: paymentType,
		paymentProvider,
	});

	if (totalAfterThis >= payableAmount) {
		await markCartWithTransactionID(transactionId);
		await completeTransaction();
	}

	onSuccess?.();
};

export const calculateBalanceDue = (
	payableAmount: number,
	payments: PaymentProps[] | undefined,
	inputAmount: number
): number => {
	const totalPaid = getTotalAmountFromPaymentHistory(payments);
	const remaining = payableAmount - (totalPaid + inputAmount || 0);
	return +remaining.toFixed(2);
};
