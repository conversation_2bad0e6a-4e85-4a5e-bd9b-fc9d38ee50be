import CompleteTransaction from '../complete-transaction';
import Paybar from './pay-bar';
import PaymentButtons from './payment-buttons';

import styles from './index.module.scss';
// import Receipt from '../receipt';
import { useGetCart } from '../../../integrations/react_query/queries';
import { ECart, EQuerykeys } from '../../../models';
import { useCartAnalytics } from '../../../hooks/useCartAnalytics';
import { getSessionInfo } from '../../../utils/getSessionInfo';
import { useMutation } from 'react-query';
import ThermalUsbPrinter from '../../../lib/printer';

export const PaymentOptions = () => {
	const thermalPrinter = new ThermalUsbPrinter();

	const { data } = useGetCart([EQuerykeys.cart, ECart.active]);

	const { payableAmount, vatAmount, totalSavings, cartSumWithoutDiscount } = useCartAnalytics(data?.products ?? []);

	//TODO: put this in cart analytics
	const totalSumOfAmountPaid = (data?.payments || [])?.reduce((acc: any, p: any) => acc + +p?.amountPaid, 0);

	const { userInfo } = getSessionInfo();

	const { mutateAsync: print, isLoading: isPrinting } = useMutation({
		mutationFn: async () => {
			if (!data) return;

			thermalPrinter.printReceipt({
				data,
				userInfo,
				cartSumWithoutDiscount: cartSumWithoutDiscount,
				totalSavings: totalSavings,
				totalSumOfAmountPaid: totalSumOfAmountPaid,
				vatAmount,
			});
		},
		onError: (err) => {
			console.log(err, 'an error occured');
		},
	});

	if (!data) return null;

	return (
		<section className={`${styles.wrapper} position-relative col-12 col-md-7 col-lg-7 col-xl-7 col-xxl-7`}>
			{!!data?.transactionId ? (
				<>
					<CompleteTransaction
						data={data}
						totalAmount={+payableAmount}
						totalPayment={totalSumOfAmountPaid}
						handlePrintReceipt={() => print()}
						isPrinting={isPrinting}
					/>

					{/* <Receipt {...{ data: cartData }} /> */}
				</>
			) : (
				<>
					<Paybar />
					<PaymentButtons />
				</>
			)}
		</section>
	);
};

export default PaymentOptions;
