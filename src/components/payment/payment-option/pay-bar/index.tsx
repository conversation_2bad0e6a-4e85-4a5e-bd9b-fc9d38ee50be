import React from 'react';

import PayAmountBar from '../../../resuable-ui/pay-amount-bar';

import styles from './index.module.scss';
import { ECart, EQuerykeys } from '../../../../models';
import { useGetCart } from '../../../../integrations/react_query/queries';

const Paybar = () => {
	const { isLoading, isError, data: cartItems } = useGetCart([EQuerykeys.cart, ECart.active]);

	if (isLoading) {
		return <div>Loading...</div>;
	}

	if (isError) {
		return <div>Error...</div>;
	}

	if (!cartItems) {
		return <div>no available data</div>;
	}

	return (
		<section className={`d-flex justify-content-between align-items-center`}>
			<div className={`${styles.pay} mx-3 mx-lg-4 mx-xl-5 mx-xxl-5`}>
				<span>Pay</span>
			</div>

			<div className="flex-fill ">
				<PayAmountBar {...{ data: cartItems }} />
			</div>
		</section>
	);
};

export default Paybar;
