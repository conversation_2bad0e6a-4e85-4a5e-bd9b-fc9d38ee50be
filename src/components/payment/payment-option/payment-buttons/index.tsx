import React from 'react';
import Button from '../../../resuable-ui/button';

import styles from './index.module.scss';

import Modal from '../../../resuable-ui/modal';
import { PaymentTypes } from '../../../../types';
import { getUserRoles } from '../../../../utils/getUserRole';
import TransferBankModalContent from '../payment-modals/transfer-bank';
import CashCreditModalContent from '../payment-modals/cash-credit';

const PaymentButtons = () => {
	const isAccountant = getUserRoles()?.isAccountant;

	const titles = {
		[PaymentTypes.card]: 'Card Payment Confirmation',
		[PaymentTypes.cash]: 'Cash Payment Confirmation',
		[PaymentTypes.credit]: 'Credit Payment Confirmation',
		[PaymentTypes.transfer]: 'Transfer Payment Confirmation',
	};

	const buttonData = [
		{
			id: 2,
			name: PaymentTypes.card,
			onClick: () => {},
		},
		{
			id: 1,
			name: PaymentTypes.cash,
			onClick: () => {},
		},
		...(isAccountant
			? [
					{
						id: 3,
						name: PaymentTypes.credit,
						onClick: () => {},
					},
				]
			: []),
		{
			id: 4,
			name: PaymentTypes.transfer,
			onClick: () => {},
		},
	];

	const [show, setShow] = React.useState(false);

	const [type, setType] = React.useState<{
		name: string;
		id: number | null;
	}>({ id: null, name: '' });

	const handleModal = (name: string, id: number) => {
		setType({ id, name });
		setShow(true);
	};

	return (
		<>
			<Modal
				{...{
					show,
					setShow,
					columnLayout: 'col-9 col-lg-8 col-xl-7 col-xxl-6',
				}}
			>
				{[2, 4].includes(type?.id ?? 0) ? (
					<TransferBankModalContent
						title={titles[type.name as PaymentTypes]}
						paymentType={type.name}
						{...{ setShow, show }}
					/>
				) : (
					<CashCreditModalContent
						title={titles[type.name as PaymentTypes]}
						paymentType={type.name}
						{...{ setShow, show }}
					/>
				)}
			</Modal>
			<section className={`${styles.wrapper}`}>
				<div className="container text-center">
					<div className="row">
						{buttonData.map(({ id, name, ...props }) => {
							return (
								<div className="col-6 my-3">
									<Button
										{...props}
										key={id}
										type="button"
										styles="w-75 py-3 py-lg-4 py-xl-4 py-xxl-4"
										onClick={() => handleModal(name, id)}
										style={{ fontWeight: 500 }}
									>
										{name}
									</Button>
								</div>
							);
						})}
					</div>
				</div>
			</section>
		</>
	);
};

export default PaymentButtons;
