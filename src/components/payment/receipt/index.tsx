import React from "react";

import NairaIcon from "../../svgs/NairaIcon";

import styles from "./index.module.scss";

import { formatAmount } from "../../../utils/funcs";

import CartSummary from "../../resuable-ui/cart-summary";

interface IReceipt {
  data: any;
}

const Receipt = ({ data }: IReceipt) => {
  return (
    <section className={`${styles.wrapper} d-flex justify-content-center`}>
      <div className={`${styles.innerWrapper}`}>
        <div className={`${styles.header} text-center`}>
          <div>
            <span>Integral</span>
          </div>
          <div className={styles.receiptId}>
            <span>Receipt No</span>: #16473628
          </div>
        </div>

        <div
          className={`d-flex align-items-center justify-content-center mt-2 ${styles.amount}`}
        >
          <NairaIcon /> &nbsp;
          {formatAmount(4500)}
        </div>

        <div
          className={`d-flex justify-content-between mt-2 py-2 ${styles.cashier}`}
        >
          <div>Cashier: <PERSON><PERSON></div>

          <div>5th April, 2022</div>

          <div>8:30am</div>
        </div>

        <div className={`${styles.order} mt-3`}>
          <span>Order details</span>
        </div>

        <div className={styles.summaryWrapper}>
          {data.map((item: any) => (
            <CartSummary
              {...{
                name: item.name,
                amount: item.amount,
                quantity: item.quantity,
                id: item.id,
                styles: styles.summary,
              }}
            />
          ))}
        </div>

        <div>
          <div className="d-flex align-items-center justify-content-between mt-3">
            <div className={styles.discount}>
              <span>Total Discount</span>
            </div>
            <div className={styles.discountAmount}>{formatAmount(7879)}</div>
          </div>

          <div className="d-flex align-items-center justify-content-between mt-1">
            <div className={styles.discount}>
              <span>VAT</span>
            </div>
            <div className={styles.vatAmount}>{formatAmount(15)}</div>
          </div>
        </div>

        <div className="mt-2">
          <div
            className={`d-flex align-items-center justify-content-between mt-1 py-1 ${styles.border}`}
          >
            <div className={`${styles.count}`}>
              <span>
                Total &nbsp;
                <span className={styles.itemsCount}>({data.length} items)</span>
              </span>
            </div>
            <div>
              <span className={styles.nairaIcon}> &#8358; </span>
              <span className={styles.itemsAmount}>{formatAmount(4550)}</span>
            </div>
          </div>

          <div
            className={`d-flex align-items-center justify-content-between mt-1 pb-3 ${styles.borderBottom} `}
          >
            <div className={`${styles.payment} `}>
              <span>
                Total &nbsp;
                <span className={styles.cashPayment}>(Cash Payment)</span>
              </span>
            </div>
            <div>
              <span className={styles.nairaIcon}> &#8358; </span>
              <span className={styles.totalAmount}>{formatAmount(4550)}</span>
            </div>
          </div>
        </div>

        <div className={`${styles.patronage} mt-2`}>
          <span>
            Thank you for your patronage.
            <br /> <br /> Not satisfied with purchase?
            <br />
            You can return a product and get a full refund or exchange
          </span>
        </div>
      </div>
    </section>
  );
};

export default Receipt;
