@import '../../../styles/mixins';

.wrapper {
	.innerWrapper {
		width: 60%;
		border: 1px solid var(--border-black);
		padding: 0.7em;

		.header {
			@include font('<PERSON><PERSON> Sans', 1em, 600);
			.receiptId {
				@include font('<PERSON><PERSON>', 0.8em, 500);
			}
		}

		.amount {
			@include font('<PERSON><PERSON> Sans', 1.4em, 400);
		}

		.cashier {
			border-bottom: 1px dashed var(--border-black);
			@include font('<PERSON><PERSON> Sans', 0.8em, 400);

			color: var(--black-color);
		}

		.order,
		.discount {
			@include font('<PERSON><PERSON> Sans', 0.9em, 500);
			margin-left: 1em;
		}

		.summaryWrapper {
			height: 25.4vh;
			overflow-y: auto;
			padding: 0 0.5em 0 0;
			@include scrollbars();
			.summary {
				color: var(--black-color);
				border: none;
				padding: 0;
				@include font('<PERSON><PERSON> Sans', 0.75em, 400);
			}
		}

		.itemsCount,
		.cashPayment {
			@include font('<PERSON><PERSON>', 0.8em, 400);
			color: var(--black-color);
		}

		.count,
		.payment {
			@include font('<PERSON><PERSON>', 0.85em, 500);
		}

		.border {
			border-bottom: 1px solid var(--border-black);
			border-top: 1px solid var(--border-black);
		}

		.borderBottom {
			border-bottom: 1px dashed var(--border-black);
		}

		.nairaIcon {
			@include nairaIcon();
		}

		.itemsAmount,
		.totalAmount,
		.vatAmount,
		.discountAmount {
			@include font('Josefin Sans', 0.9em, 400);
		}

		.patronage {
			@include font('Josefin Sans', 0.75em, 400);
			color: var(--black-color);
		}
	}
}
