import React from "react";

import { motion } from "framer-motion";

import { iconVariant } from "../../utils/anims/index";

const SettingIconSvg = () => {
  return (
    <svg
      width="23"
      height="23"
      viewBox="0 0 23 23"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <motion.path
        d="M11.5 14.364C13.0815 14.364 14.3636 13.0819 14.3636 11.5004C14.3636 9.91881 13.0815 8.63672 11.5 8.63672C9.91845 8.63672 8.63636 9.91881 8.63636 11.5004C8.63636 13.0819 9.91845 14.364 11.5 14.364Z"
        // stroke="#7F34A6"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        variants={iconVariant}
        initial="hidden"
        animate="visible"
        transition={{
          default: { duration: 3, ease: "easeInOut" },
          fill: { duration: 2, ease: [1, 0, 0.8, 1] },
        }}
      />
      <motion.path
        d="M18.5636 14.3636C18.4366 14.6515 18.3987 14.9709 18.4548 15.2806C18.511 15.5902 18.6586 15.8759 18.8786 16.1009L18.9359 16.1582C19.1134 16.3355 19.2542 16.546 19.3503 16.7778C19.4464 17.0096 19.4958 17.258 19.4958 17.5089C19.4958 17.7597 19.4464 18.0082 19.3503 18.2399C19.2542 18.4717 19.1134 18.6822 18.9359 18.8595C18.7586 19.037 18.5481 19.1779 18.3163 19.2739C18.0845 19.37 17.8361 19.4195 17.5852 19.4195C17.3343 19.4195 17.0859 19.37 16.8542 19.2739C16.6224 19.1779 16.4118 19.037 16.2345 18.8595L16.1773 18.8023C15.9523 18.5822 15.6666 18.4346 15.3569 18.3785C15.0473 18.3223 14.7279 18.3602 14.44 18.4873C14.1577 18.6083 13.9169 18.8092 13.7473 19.0653C13.5777 19.3214 13.4867 19.6215 13.4855 19.9286V20.0909C13.4855 20.5972 13.2843 21.0828 12.9263 21.4408C12.5683 21.7989 12.0827 22 11.5764 22C11.07 22 10.5845 21.7989 10.2264 21.4408C9.86841 21.0828 9.66727 20.5972 9.66727 20.0909V20.005C9.65988 19.6891 9.55761 19.3826 9.37376 19.1256C9.18991 18.8685 8.93297 18.6727 8.63636 18.5636C8.34846 18.4366 8.02909 18.3987 7.71944 18.4548C7.40979 18.511 7.12406 18.6586 6.89909 18.8786L6.84182 18.9359C6.66451 19.1134 6.45396 19.2542 6.2222 19.3503C5.99044 19.4464 5.74202 19.4958 5.49114 19.4958C5.24025 19.4958 4.99183 19.4464 4.76007 19.3503C4.52831 19.2542 4.31776 19.1134 4.14045 18.9359C3.96295 18.7586 3.82214 18.5481 3.72607 18.3163C3.62999 18.0845 3.58054 17.8361 3.58054 17.5852C3.58054 17.3343 3.62999 17.0859 3.72607 16.8542C3.82214 16.6224 3.96295 16.4118 4.14045 16.2345L4.19773 16.1773C4.41779 15.9523 4.5654 15.6666 4.62155 15.3569C4.67769 15.0473 4.63979 14.7279 4.51273 14.44C4.39173 14.1577 4.19081 13.9169 3.93472 13.7473C3.67862 13.5777 3.37852 13.4867 3.07136 13.4855H2.90909C2.40277 13.4855 1.91718 13.2843 1.55916 12.9263C1.20114 12.5683 1 12.0827 1 11.5764C1 11.07 1.20114 10.5845 1.55916 10.2264C1.91718 9.86841 2.40277 9.66727 2.90909 9.66727H2.995C3.31095 9.65988 3.61737 9.55761 3.87442 9.37376C4.13148 9.18991 4.32727 8.93297 4.43636 8.63636C4.56343 8.34846 4.60133 8.02909 4.54519 7.71944C4.48904 7.40979 4.34142 7.12406 4.12136 6.89909L4.06409 6.84182C3.88659 6.66451 3.74578 6.45396 3.6497 6.2222C3.55363 5.99044 3.50418 5.74202 3.50418 5.49114C3.50418 5.24025 3.55363 4.99183 3.6497 4.76007C3.74578 4.52831 3.88659 4.31776 4.06409 4.14045C4.24139 3.96295 4.45194 3.82214 4.6837 3.72607C4.91546 3.62999 5.16389 3.58054 5.41477 3.58054C5.66566 3.58054 5.91408 3.62999 6.14584 3.72607C6.3776 3.82214 6.58815 3.96295 6.76545 4.14045L6.82273 4.19773C7.04769 4.41779 7.33342 4.5654 7.64307 4.62155C7.95272 4.67769 8.27209 4.63979 8.56 4.51273H8.63636C8.91869 4.39173 9.15947 4.19081 9.32907 3.93472C9.49867 3.67862 9.58968 3.37852 9.59091 3.07136V2.90909C9.59091 2.40277 9.79205 1.91718 10.1501 1.55916C10.5081 1.20114 10.9937 1 11.5 1C12.0063 1 12.4919 1.20114 12.8499 1.55916C13.208 1.91718 13.4091 2.40277 13.4091 2.90909V2.995C13.4103 3.30216 13.5013 3.60226 13.6709 3.85835C13.8405 4.11445 14.0813 4.31536 14.3636 4.43636C14.6515 4.56343 14.9709 4.60133 15.2806 4.54519C15.5902 4.48904 15.8759 4.34142 16.1009 4.12136L16.1582 4.06409C16.3355 3.88659 16.546 3.74578 16.7778 3.6497C17.0096 3.55363 17.258 3.50418 17.5089 3.50418C17.7597 3.50418 18.0082 3.55363 18.2399 3.6497C18.4717 3.74578 18.6822 3.88659 18.8595 4.06409C19.037 4.24139 19.1779 4.45194 19.2739 4.6837C19.37 4.91546 19.4195 5.16389 19.4195 5.41477C19.4195 5.66566 19.37 5.91408 19.2739 6.14584C19.1779 6.3776 19.037 6.58815 18.8595 6.76545L18.8023 6.82273C18.5822 7.04769 18.4346 7.33342 18.3785 7.64307C18.3223 7.95272 18.3602 8.27209 18.4873 8.56V8.63636C18.6083 8.91869 18.8092 9.15947 19.0653 9.32907C19.3214 9.49867 19.6215 9.58968 19.9286 9.59091H20.0909C20.5972 9.59091 21.0828 9.79205 21.4408 10.1501C21.7989 10.5081 22 10.9937 22 11.5C22 12.0063 21.7989 12.4919 21.4408 12.8499C21.0828 13.208 20.5972 13.4091 20.0909 13.4091H20.005C19.6978 13.4103 19.3977 13.5013 19.1416 13.6709C18.8856 13.8405 18.6846 14.0813 18.5636 14.3636V14.3636Z"
        // stroke="#7F5297"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        variants={iconVariant}
        initial="hidden"
        animate="visible"
        transition={{
          default: { duration: 1, ease: "easeInOut" },
          fill: { duration: 2, ease: [1, 0, 0.8, 1] },
        }}
      />
    </svg>
  );
};

export default SettingIconSvg;
