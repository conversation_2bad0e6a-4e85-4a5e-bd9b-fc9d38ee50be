import React from "react";

const MarkIconSvg = () => {
  return (
    <svg
      width="112"
      height="115"
      viewBox="0 0 112 115"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <ellipse cx="56" cy="108" rx="56" ry="7" fill="#767676" />
      <circle cx="56" cy="54" r="54" fill="#51D4A7" />
      <path
        d="M38.6361 76.419L38.6362 76.419C39.7169 77.4757 41.1303 78 42.5295 78C43.928 78 45.3423 77.4762 46.4229 76.419C46.4231 76.4188 46.4233 76.4186 46.4236 76.4183L84.3634 39.3218L84.3638 39.3215C86.5454 37.1874 86.5454 33.7163 84.3638 31.5821L84.3631 31.5815C82.2058 29.473 78.7339 29.4726 76.5767 31.5818L42.5296 64.8721L31.4238 54.013C29.2665 51.9037 25.7946 51.903 23.6374 54.0126C21.4543 56.1462 21.4542 59.6187 23.637 61.7524C23.6371 61.7525 23.6372 61.7526 23.6374 61.7527L38.6361 76.419Z"
        fill="white"
        stroke="white"
        stroke-width="4"
      />
    </svg>
  );
};

export default MarkIconSvg;
