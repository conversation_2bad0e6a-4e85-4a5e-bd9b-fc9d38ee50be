import React from 'react';
import PinInput from '../../pin-input';
import { createPortal } from 'react-dom';
import { useVerifyPasscode } from '../../../integrations/react_query/mutations';
import { convertObjectToString } from '../../../utils/funcs';
import Verification from '../../resuable-ui/verification';

const PincodeAuth = () => {
	const [confirmValues, setConfirmValues] = React.useState({
		'0': '',
		'1': '',
		'2': '',
		'3': '',
		'4': '',
	});

	const { mutate, isLoading } = useVerifyPasscode();

	const handleVerifyPasscode = () => {
		const confirmValuesString = convertObjectToString(confirmValues);

		if (!confirmValuesString) return;

		if (confirmValuesString.length < 5) return;

		mutate(confirmValuesString);
	};

	const elem = createPortal(
		<div
			style={{
				position: 'absolute',
				width: '100%',
				top: 0,
				backdropFilter: 'blur(2.5px)',
				background: 'rgb(126, 125, 125, 0.7)',
			}}
		>
			<section className="container">
				<section className="row justify-content-center align-items-center overflow-hidden" style={{ height: '100vh' }}>
					<section className="col-12 col-md-9 col-xl-6 col-xxl-6 col-lg-7">
						<div
							className="position-relative bg-white overflow-hidden rounded shadow-md"
							style={{ padding: '3rem 0 2rem ' }}
						>
							<Verification
								{...{
									text1: 'Provide your passcode',
									text3: "note: it would only asked when you're idle",
								}}
							/>

							{isLoading && (
								<div
									className="position-absolute text-center d-flex justify-content-center"
									style={{ width: '100%', height: '', bottom: 200 }}
								>
									<div
										className="d-flex justify-content-center align-items-center p-2 rounded shadow"
										style={{ background: 'var(--white-color)', width: 'fit-content' }}
									>
										<div style={{ color: 'var(--main-color)' }} className="spinner-grow spinner-grow-sm"></div>
										<div
											style={{
												color: 'var(--main-color-extra-light)',
											}}
											className="mx-2  text-sm"
										>
											Verifying...
										</div>
									</div>
								</div>
							)}

							<PinInput
								showOk
								{...{
									values: confirmValues,
									setValues: setConfirmValues,
									v: true,
									setIsEqualPin: () => {},
									handleOkClick: handleVerifyPasscode,
								}}
							/>
						</div>
					</section>
				</section>
			</section>
		</div>,
		document.body
	);

	return elem;
};

export default PincodeAuth;
