import React from 'react';
import {
	WiDaySunny,
	WiCloud,
	Wi<PERSON>loudy,
	WiFog,
	WiRain,
	WiThunderstorm,
	WiShowers,
	WiNightClear,
	WiNightAltCloudy,
} from 'react-icons/wi';
import { useQuery } from 'react-query';

type WeatherData = {
	temperature: number;
	description: string;
	icon: any;
};

const weatherCodeMap: Record<number, Omit<WeatherData, 'temperature'>> = {
	0: { description: 'Clear sky', icon: WiDaySunny },
	1: { description: 'Mostly clear', icon: WiDaySunny },
	2: { description: 'Partly cloudy', icon: WiCloud },
	3: { description: 'Overcast', icon: WiCloudy },
	45: { description: 'Foggy', icon: WiFog },
	48: { description: 'Rime fog', icon: WiFog },
	51: { description: 'Light drizzle', icon: WiShowers },
	53: { description: 'Drizzle', icon: WiShowers },
	55: { description: 'Heavy drizzle', icon: WiShow<PERSON> },
	61: { description: 'Light rain', icon: WiRain },
	63: { description: 'Rain', icon: WiRain },
	65: { description: 'Heavy rain', icon: WiRain },
	80: { description: 'Rain showers', icon: WiShowers },
	95: { description: 'Thunderstorm', icon: WiThunderstorm },
	99: { description: 'Storm with hail', icon: WiThunderstorm },
};

const nightIconMap: Partial<Record<number, any>> = {
	0: WiNightClear,
	1: WiNightClear,
	2: WiNightAltCloudy,
	3: WiCloudy,
};

const fetchWeatherData = async (): Promise<WeatherData | null> => {
	return new Promise((resolve) => {
		if (!navigator.geolocation) return resolve(null);

		navigator.geolocation.getCurrentPosition(
			async (position) => {
				try {
					const { latitude, longitude } = position.coords;

					const weatherRes = await fetch(
						`https://api.open-meteo.com/v1/forecast?latitude=${latitude}&longitude=${longitude}&current=temperature_2m,weathercode,is_day`
					);
					const weatherJson = await weatherRes.json();
					const { temperature_2m, weathercode, is_day } = weatherJson.current;

					const base = weatherCodeMap[weathercode] ?? {
						description: 'Weather unavailable',
						icon: WiDaySunny,
					};

					const icon = is_day ? base.icon : (nightIconMap[weathercode] ?? base.icon);
					const timeLabel = is_day ? 'day' : 'night';

					resolve({
						temperature: temperature_2m,
						description: `${base.description} (${timeLabel})`,
						icon,
					});
				} catch (e) {
					console.error(e);
					resolve(null);
				}
			},
			(err) => {
				console.warn(err);
				resolve(null);
			}
		);
	});
};

const WeatherWidget = () => {
	const {
		data: weather,
		isLoading,
		error,
	} = useQuery('weather', fetchWeatherData, {
		staleTime: 1000 * 60 * 60, // 1 hour
	});

	return (
		<div style={{ display: 'flex', alignItems: 'center', gap: 8, fontSize: 16, color: 'white' }}>
			{isLoading ? (
				<span>Pinging weather info...</span>
			) : error || !weather ? (
				<span>Weather unavailable</span>
			) : (
				<>
					<span style={{ fontSize: 24 }}>
						<weather.icon color="white" size={46} />
					</span>
					<span>
						{Math.floor(weather.temperature)}°C
						{/* – {weather.description} */}
					</span>
				</>
			)}
		</div>
	);
};

export default WeatherWidget;
