import { toast, ToastPosition } from "react-toastify";

type ToastConstraints = "success" | "info" | "error";

interface ITriggerToast {
  type: ToastConstraints;
  message: string;
  duration?: number | number | undefined;
  position?: ToastPosition;
}

export const triggerToast = ({
  type,
  message = "Hello from shoppa app notification",
  duration = 3000,
  position = "top-right",
}: ITriggerToast) => {
  toast[type](message, {
    position,
    autoClose: duration,
    hideProgressBar: false,
    closeOnClick: true,
    pauseOnHover: false,
    draggable: true,
    progress: undefined,
  });
};
