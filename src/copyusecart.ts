// import {
//     useAddToCart,
//     useUpdateCart,
// } from "../integrations/react_query/mutations";

// import { saveSession, getSession } from "../utils/storage";

// import { getEnvs } from "./../utils/getEnvs";
// import { genId } from "./../utils/genId";
// import { ECart, EThrottles } from "../models";

// const { cartId, user } = getEnvs();

// let throttleRef: any;

// export const useCart = (event?: "change" | "click", ref?: any) => {
//     const { mutate } = useAddToCart();
//     const { mutate: mutateUpdate } = useUpdateCart(event);

//     const userInfo: any = getSession(user);

//     throttleRef = setTimeout(
//         () => {
//             throttleRef = undefined;
//         },
//         event === "change" ? EThrottles.change : EThrottles.click
//     );

//     const handleAddToCart = (item: any) => {
//         if (throttleRef) return;

//         const {
//             name,
//             price = 100,
//             location,
//             discount = 4,
//             branch,
//             quantity = 1,
//             _id: productId,
//         } = item;

//         const product = { productId, name, price, discount, quantity };

//         if (!getSession(cartId + `-${userInfo.name}`)) {
//             saveSession(cartId + `-${userInfo.name}`, genId());

//             const product = {
//                 productId,
//                 name,
//                 price,
//                 discount,
//                 quantity: 1,
//                 refCartId: getSession(cartId + `-${userInfo.name}`),
//             };

//             const payload = {
//                 _id: getSession(cartId + `-${userInfo.name}`),
//                 cashier: userInfo.name,
//                 branch,
//                 location,
//                 type: ECart.cartType,
//                 status: ECart.active,
//                 createdAt: new Date(),
//                 products: [product],
//                 customer: "",
//             };

//             mutate(payload);
//         } else {
//             const refCartId = getSession(cartId + `-${userInfo.name}`);

//             if (event === "change") {
//                 mutateUpdate({ ...item, refCartId });
//                 return;
//             }
//             mutateUpdate({ ...product, refCartId });
//         }
//     };
//     return { handleAddToCart };
// };

export const a = {}
