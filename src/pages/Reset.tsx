import React from 'react';

import PinInput from '../components/pin-input';
import ConfirmationBtn from '../components/resuable-ui/confirm-btn';
import Verification from '../components/resuable-ui/verification';

import { useNavigate } from 'react-router-dom';
import { usePinCode } from '../integrations/react_query/mutations';

import { checkPinValidity, convertObjectToString, checkIsEqualPin } from '../utils/funcs';

const Reset = () => {
	const navigate = useNavigate();

	const [values, setValues] = React.useState({
		'0': '',
		'1': '',
		'2': '',
		'3': '',
		'4': '',
	});

	const [confirmValues, setConfirmValues] = React.useState({
		'0': '',
		'1': '',
		'2': '',
		'3': '',
		'4': '',
	});

	const [isConfirm, setIsConfirm] = React.useState(false);

	const handleGoToConfirmScreen = () => {
		checkPinValidity(values) && setIsConfirm(true);
	};

	const [isEqualPin, setIsEqualPin] = React.useState(true);

	const { mutate, isLoading } = usePinCode();

	const handlePinProcess = () => {
		if (!checkPinValidity(confirmValues)) return;

		if (!checkIsEqualPin(values, confirmValues)) {
			setIsEqualPin(false);
			return;
		}

		mutate(convertObjectToString(values));
	};

	const renderError = (arg: boolean, message: string) => (
		<>{!arg && <div className="col-12 text-center mb-3 text-danger fw-bold">{message}</div>}</>
	);

	return (
		<section className="container">
			<section className="row justify-content-center align-items-center overflow-hidden" style={{ height: '100vh' }}>
				<section className="col-12">
					<section className="row justify-content-center">
						{!isConfirm ? (
							<>
								<Verification
									{...{
										text1: 'Set Your Passcode',
										text3: 'tip: use a pin you can remember',
									}}
								/>

								<PinInput {...{ values, setValues, v: false, setIsEqualPin }} />

								<ConfirmationBtn
									{...{
										handleClick: () => handleGoToConfirmScreen(),
										text: 'Continue',
										handleBack: () => navigate(-1),
										showBackBtn: false,
									}}
								/>
							</>
						) : (
							<>
								<Verification
									{...{
										text1: 'Re-enter New PIN',
										text2:
											'You are advised to personalize your PIN. <br> You can reset forgotten password or contact Admin.',
										text3: 'tip: Use a familiar number',
									}}
								/>

								{renderError(isEqualPin, 'Pins do not match. Try Again.')}

								<PinInput
									{...{
										values: confirmValues,
										setValues: setConfirmValues,
										v: true,
										setIsEqualPin,
									}}
								/>

								<ConfirmationBtn
									{...{
										handleClick: () => handlePinProcess(),
										text: `${isLoading ? 'Confirming...' : 'Confirm'}`,
										handleBack: () => setIsConfirm(false),
										showBackBtn: true,
									}}
								/>
							</>
						)}
					</section>
				</section>
			</section>
		</section>
	);
};

export default Reset;
