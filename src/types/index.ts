export type UserResponseProps = {
	_id: string;
	userId: string;
	type: string;
	roles: string[];
	merchantName: string;
	passcode: string | null;
	flags: {
		isDeleted: boolean;
		emailVerified: boolean;
		phoneVerified: boolean;
		accountLocked: boolean;
		pinCodeEnabled: boolean;
	};
	assignedLocations: {
		id: string;
		city: string;
		state: string;
		address: string;
	}[];
	profile: {
		firstName: string;
		lastName: string;
		email: string;
		address: string;
		phoneNumber: string;
		avatarUrl: string | null;
		state: string | null;
		city: string | null;
	};
	createdAt: string;
	updatedAt: string;
};

export type CompanyInfoResponseProps = {
	_id: string;
	_rev: string;
	businessName: string;
	phoneNumber: string;
	otherPhoneNumber: string;
	address: string;
	logo: string | null;
	email: string;
	website: string | null;
	additionalFields: Record<string, any> | null;
	tax: number | null;
};

export enum PaymentTypes {
	card = 'Card',
	credit = 'Credit',
	cash = 'Cash',
	transfer = 'Transfer',
}

export enum roles {
	owner = 'owner',
	manager = 'manager',
	cashier = 'cashier',
	accountant = 'accountant',
}
