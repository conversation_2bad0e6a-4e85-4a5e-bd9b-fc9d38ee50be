import { IdleTimer<PERSON>rovider } from 'react-idle-timer';
import { useLocation } from 'react-router-dom';
import { updatePinCodeStatus } from '../utils/pincode';
import { useAppDispatch } from '../store/storeConfig';
import React from 'react';
import { handleShowPinCodeScreen } from '../store/slices/authSlice';

function IdleTimerProviderWrapper({ children }: { children: React.ReactNode }) {
	const location = useLocation();

	const dispatch = useAppDispatch();

	const onPrompt = async () => {
		if (location.pathname === '/terminal' || location.pathname === '/payment') {
			await updatePinCodeStatus(false);
			dispatch(handleShowPinCodeScreen(true));
			return;
		} else {
			dispatch(handleShowPinCodeScreen(false));
		}
	};

	const onIdle = () => null;

	return (
		<IdleTimerProvider timeout={11_00000} promptBeforeIdle={10_000} onPrompt={onPrompt} onIdle={onIdle}>
			{children}{' '}
		</IdleTimerProvider>
	);
}

export default React.memo(IdleTimerProviderWrapper);
