import { PaymentTypes } from '../types';
import { isDiscountValid } from './../utils/isDiscountValid';
export type SalesProps = {
	productId: string;
	productNo: string | null;
	productName: string;
	sku: string | null;
	sellingPrice: number | null;
	discount: number | null;
	quantity: number;
	refCartId: string | null;
	variantId: number | null;
	unitCost: number | null; // this can be optional as it won't exist every time
	isVoid: boolean;
	isDiscountValid: boolean;
};

export type CustomerTransactionProps = {
	name: string;
	id: string;
	phoneNumber: string | null;
	email: string | null;
	birthday?: {
		month: string;
		day: string;
	} | null;
};

export type CartProps = {
	_id: string;
	cashier: Record<string, string>;
	merchantName: string;
	type: string;
	status: string;
	createdAt: string;
	updatedAt: string;
	products: SalesProps[];
	customer: CustomerTransactionProps | null | undefined;
	note: string | null;
	// pricing: Pricing
};

export type PaymentMethods = 'cash' | 'transfer' | 'card';

export type PaymentProps = {
	method: PaymentTypes;
	amountPaid: number;
	note: string | null;
	id?: number | string;
	paymentProvider?: Record<string, unknown> | null;
};

export type SalesTransactionResponseProps = {
	_id: string;
	salesId: string;
	invoiceId: string;
	isVoid: boolean;
	cashier: Record<string, string>;
	merchantName: string;
	type: string;
	status: string;
	createdAt: string;
	updatedAt: string;
	products: SalesProps[];
	customer: CustomerTransactionProps | null | undefined;
	payments: PaymentProps[];
	transactionId: string;
	note: string | null;
	salesPerson?: Record<string, string>;
};

// export type UserProps = {
// 	_id: string;
// 	_rev: string;
// 	type: string;
// 	name: string;
// 	roles: string[];
// 	merchant_name: string; //todo: this must be in camel case
// 	merchantName?: string; //this does not really exists for now
// 	passcode: string;
// 	firstName: string;
// 	lastName: string;
// 	userName: string;
// 	role: string;
// 	password_scheme: string;
// 	iterations: number;
// 	derived_key: string;
// 	salt: string;
// };

export type AddProductItemProps = {
	isActive: boolean | null;
	productId: string;
	productName: string | null;
	sku: string | null;
	productImage: string | null;
	brandName: string | null;
	barcode: string;
	inventoryType: string | null;
	description: string | null;
	categories: string[] | null;
	tags: string[] | null;
	unit: string;
	identifiers: {
		ean: string | null;
		upc: string | null;
		mpn: string | null;
		isbn: string | null;
	};
	pricing: {
		sellingPrice: number | null;
	};
	discount: {
		discountAmount: number | null;
		discountPercentage: number | null;
		discountValidFrom: string | null;
		discountValidUntil: string | null;
	};
	isVariantProduct: boolean;
	inventory: {
		openingStock: number | null;
		reOrderLevel: number | null;
		unitCost: number | null;
		quantityAvailable?: number;
		averageUnitCost: number | null;
	};
	manufacturer: string | null;
	hasWarrantyCoverage: boolean | null;
	hasExpiryDate: boolean | null;
	variants:
		| {
				id: number;
				sku: string | null;
				isActive: boolean;
				inventory: {
					openingStock: number | null;
					reOrderLevel: number | null;
					unitCost: number | null;
					quantityAvailable?: number;
					averageUnitCost: number | null;
				};
				hasExpiryDate: boolean | null;
				pricing: {
					sellingPrice: number | null;
				};
				warranty: string | null;
				productImage: string | null;
				hasWarrantyCoverage: boolean | null;
				returnDuration: number | null;
				attributes: Record<string, any> | null;
				identifiers: {
					ean: string | null;
					upc: string | null;
					mpn: string | null;
					isbn: string | null;
				};
				discount: {
					discountAmount: number | null;
					discountPercentage: number | null;
					discountValidFrom: string | null;
					discountValidUntil: string | null;
				};
		  }[]
		| null;
	variantAttributes?:
		| {
				attribute: string;
				values: string[];
		  }[]
		| null;
};

export type Pricing = {
	sellingPrice: number;
	discountAmount: number;
	discountPercentage: number;
	discountValidFrom: string | null;
	discountValidUntil: string | null;
};

type Inventory = {
	openingStock: number;
	quantityAvailable: number;
	reOrderLevel: number | null;
	unitCost: number | null;
};
export type ProductVariant = {
	id: string | null;
	sku: string | null;
	productImage: string | null;
	attributes: Record<string, any> | null;
	identifiers: Record<string, any> | null;
	pricing: Pricing;
	inventory: Inventory;
	isActive: boolean;
	hasWarrantyCoverage: boolean;
	hasExpiryDate: boolean;
};

export type FlattenedProductProps = {
	merchantName: string;
	_id: string;
	productId: string;
	barcode: string;
	variantId: number | null;
	productName: string;
	description: string | null;
	categories: string[] | null;
	tags: string[] | null;
	sku: string | null;
	productImage: string | null;
	attributes: Record<string, string> | null;
	identifiers: AddProductItemProps['identifiers'] | null;
	pricing: Pricing;
	inventory: Inventory;
	isVariantProduct: boolean;
	hasWarrantyCoverage: boolean;
	hasExpiryDate: boolean;
	createdAt: string;
	updatedAt: string;
};

export type ProductItemProps = AddProductItemProps & {
	_id: string;
	_rev: string;
	merchantName: string;
	type: 'product';
	createdAt: string;
	updatedAt: string;
};

export interface IAuthInterface {
	body: string;
}

export interface IRoutes {
	path: string;
	isProtected: Boolean;
	element: React.ExoticComponent;
	children?: any;
}

export interface IinputField {
	id: string;
	onChange: (arg?: any) => void;
	label?: string;
	type: string;
	touched: any;
	value: string | number;
	error: string | undefined;
	handlePassword?: () => void;
	password?: boolean;
	showIcon?: boolean | undefined;
	[key: string]: any;
}

export interface IButton {
	[key: string]: any;
	isBordered?: boolean;
}

export interface ITextBox {
	[key: string]: any;
}

export interface IButtonPad {
	handleClear: (param?: any) => void;
	handleBackSpace: (param?: any) => void;
	handleClick: (param?: any) => void;
	handleOkClick?: () => void;
	showOk?: boolean;
}

export interface IPinInput {
	values: any;
	setValues: (param?: any) => void;
	v: boolean;
	setIsEqualPin: (arg: boolean) => void;
	handleOkClick?: () => void;
	showOk?: boolean;
}

export interface IVerification {
	text1: string;
	text2?: string;
	text3: string;
}

export interface IconfirmationBtn {
	handleClick: (arg?: any) => void;
	handleBack?: () => void;
	text: string;
	showBackBtn: boolean;
}

export interface IMenuToggle {
	handleToggle: (arg?: any) => void;
	status: boolean;
}

export interface ISideMenuData {
	id: number;
	name: string;
	component: React.FC;
	path: string;
}

interface navState {
	navState: boolean;
	setNavState: (arg: boolean) => void;
	showBar?: boolean;
}

export interface ISideMenu extends navState {}

export interface INavBar extends navState {}

export interface IPopUp {
	[key: string]: any;
	show: boolean;
	setShow?: React.Dispatch<React.SetStateAction<boolean>>;
	withBg?: boolean;
	children: React.ReactNode;
}

export interface ICatetogryList {
	id: number;
	name: string;
	quantity: number;
}

export interface IPopUpPosition {
	x: number;
	y: number;
	width?: number | string;
	styleClass?: any;
	children: React.ReactNode;
}

export interface IProductNav {
	onChange: React.ChangeEventHandler<HTMLInputElement>;
	toggle: Boolean;
	setToggle: (arg: boolean) => void;
	value: string;
}

export interface ICartH {
	quanity: number;
	product: string;
	amount: number;
	id: number;
}

export interface IModal {
	setShow: (arg: boolean) => void;
	show: boolean;
	children: React.ReactNode;
	columnLayout: string;
}

export interface IPincode {
	_id: string;
	password: string;
}

export interface IFilter {
	filter: string;
	query_params: {
		type: string;
		locationId: string;
		[key: string]: string;
	};
}

export enum EDDocId {
	product = '_design/generic',
}

//export enum EDDocTypes {
//  product = "product",
//}

export enum EDDocFilterKeys {
	product = 'product',
	sales = 'sales',
	cashier = 'cashier',
	customer = 'customer',
	companySettings = 'companySettings',
	payment_types = 'paymentType',
}

export enum EDDocMapKeys {
	category = 'category',
}

export enum EQuerykeys {
	product = 'products',
	cart = 'carts',
	sales = 'sales',
	cashier = 'cashier',
	customer = 'customer',
	payment_types = 'payment-types',
}

export enum ELocalDBNames {
	product = 'products',
	sales = 'sales',
	cashiers = 'cashiers',
	customer = 'customer',
	company_settings = 'company_settings',
	payment_types = 'payment_types',
}

export enum EDDocPath {
	product_filter_initial = `generic/product-filter-initial`,
	product_filter_subsequent = `generic/product-filter-subsequent`,
	cashier_filter = `generic/cashier-filter`,
	category = `generic/category`,
	sales = `generic/sales`,
	customer = 'generic/customer',
	company_settings = 'generic/company-settings-filter',
	payment_types = 'generic/payment-types-filter',
}

export enum ECart {
	cartType = 'cart',
	active = 'active',
	parked = 'parked',
	discarded = 'discarded',
	completed = 'completed',
}

type EnumExtract<T> = T[keyof T];

export type CartStatuses = EnumExtract<typeof ECart>;

export enum EThrottles {
	change = 500,
	click = 3000,
}

export enum EVat {
	Vat = 0.075,
}
export interface IDeleteCartItem {
	refCartId: string;
	productId: string;
	name: string;
}

export enum SalesStatus {
	pending = 'pending',
	failed = 'failed',
	processed = 'processed',
	processing = 'processing',
}

export enum DocumentPrefixes {
	SALES_ORDER = 'SO',
	ADJUSTMENT = 'ADJ',
	TRANSFER = 'TR',
	PURCHASE_ORDER = 'PO',
	INVOICE = 'INV',
	CUSTOMER = 'CUS',
	PRODUCT = 'PR',
	SUPPLIER = 'SUP',
	PAYMENT = 'PAY',
	REFUND = 'RF',
	VOID = 'VOID',
}

export type FilterBy = 'today' | 'week' | 'month' | 'year';
