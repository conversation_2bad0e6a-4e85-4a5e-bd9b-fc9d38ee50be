import { formatAmount } from './../utils/funcs';
import dayjs from 'dayjs';
import { UserResponseProps } from '../types';
import { CartProps, PaymentProps } from '../models';

type ThermalUsbPrinterProps = {
	printReceipt: ({
		data,
	}: {
		data: CartProps & {
			payments?: PaymentProps[] | undefined;
			transactionId?: number | undefined;
		};
		userInfo: UserResponseProps;
		totalSumOfAmountPaid: string;
		totalSavings: string;
		cartSumWithoutDiscount: string;
		vatAmount: string;
	}) => Promise<void>;
	getConnectedUsb: () => Promise<USBDevice[]>;
};

class ThermalUsbPrinter implements ThermalUsbPrinterProps {
	thermalPrinterUsbDevice: USBDevice | null = null;

	constructor() {
		if (this.confirmWebUsbSupport()) {
			this.reconnectAvailableUsbDevice();
			navigator.usb.onconnect = this.handleUsbConnect;
			navigator.usb.ondisconnect = this.handleUsbDisconnect;
		}
	}

	confirmWebUsbSupport = () => {
		if (!this.supportWebUsbApi()) return false;
		return true;
	};

	supportWebUsbApi = () => !!navigator.usb;

	handleUsbConnect = (event: USBConnectionEvent) => {
		this.thermalPrinterUsbDevice = event.device;
	};

	handleUsbDisconnect = (event: USBConnectionEvent) => {
		if (this.thermalPrinterUsbDevice === event.device) {
			this.thermalPrinterUsbDevice = null;
		}
	};

	getConnectedUsb = async () => {
		return await navigator.usb.getDevices();
	};

	guessPrinter = (printerName: string | undefined) => {
		return (
			printerName?.toLowerCase()?.includes('thermal') ||
			printerName?.toLowerCase()?.includes('printer') ||
			printerName?.toLowerCase()?.includes('pos')
		);
	};

	reconnectAvailableUsbDevice = async () => {
		const connectedUsbDevices = await this.getConnectedUsb();
		if (connectedUsbDevices.length <= 0) return;

		const thermalPrinter = connectedUsbDevices.find((device) => this.guessPrinter(device.productName));

		if (thermalPrinter) this.thermalPrinterUsbDevice = thermalPrinter;
		else if (connectedUsbDevices.length > 0 && !thermalPrinter)
			alert('Could not auto connect your printer. Please try again');
		else this.requestUsbConnection();
	};

	requestUsbConnection = async () => {
		//TODO: use ls to capture printer.
		//TODO: invalidate the capture via analysis
		try {
			const usbDevice = await navigator.usb.requestDevice({ filters: [] });
			this.thermalPrinterUsbDevice = usbDevice;
		} catch (err) {
			alert('No USB device selected or connected');
		}
	};

	printReceipt = async ({
		data,
		userInfo,
		totalSumOfAmountPaid,
		totalSavings,
		vatAmount,
		cartSumWithoutDiscount,
	}: {
		data: CartProps & {
			payments?: PaymentProps[] | undefined;
			transactionId?: number | undefined;
		};
		userInfo: UserResponseProps | undefined;
		totalSumOfAmountPaid: string;
		totalSavings: string;
		cartSumWithoutDiscount: string;
		vatAmount: string;
	}) => {
		try {
			if (!this.thermalPrinterUsbDevice) {
				await this.requestUsbConnection();
			}

			await this.thermalPrinterUsbDevice?.open();
			await this.thermalPrinterUsbDevice?.selectConfiguration(1);
			await this.thermalPrinterUsbDevice?.claimInterface(0);

			const ESC = '\x1B';
			const GS = '\x1D';

			const toBytes = (str: string) => new Uint8Array(new TextEncoder().encode(str));

			const lineWidth = 47;
			const productColWidth = Math.floor(lineWidth * 0.5);
			const qtyColWidth = Math.floor(lineWidth * 0.2);
			const priceColWidth = lineWidth - productColWidth - qtyColWidth;

			const formatRow = (name: string, qty: any, price: string | number) => {
				const trimmedName = name?.length > productColWidth ? name?.slice(0, productColWidth - 1) + '…' : name;
				const paddedName = trimmedName.padEnd(productColWidth, ' ');
				const paddedQty = String(qty).padStart(qtyColWidth, ' ');
				const paddedPrice = price?.toLocaleString()?.padStart(priceColWidth, ' ');
				return `${paddedName}${paddedQty}${paddedPrice}\n`;
			};

            // NGA | Tel: 09099050867\r\n

			let receipt = `${GS}!${String.fromCharCode(0)}`;
			receipt += `${ESC}a1${ESC}E1${userInfo?.merchantName?.toUpperCase()}\r\n`;
			receipt += `${userInfo?.assignedLocations?.[0]?.city}, ${userInfo?.assignedLocations?.[0]?.address}\r\n${ESC}E0\r\n`;

			receipt += `${ESC}a2${dayjs(data?.createdAt).format('MMM. D, YYYY h:mm A')}\n`;
			receipt += `${ESC}a0Receipt No: ${data?.transactionId}\n`;
			receipt += `Seller: ${userInfo?.profile?.lastName} ${userInfo?.profile?.firstName}\n\r\n`;

			receipt += formatRow('Product', 'Qty', 'Price');
			receipt += `------------------------------------------------\n`;

			data.products.forEach((product) => {
				const name = product?.productName?.slice(0, 22) || '';
				const qty = product.quantity;
				const price = formatAmount(product?.sellingPrice || 0);
				receipt += formatRow(name, qty, price);
			});

			receipt += `------------------------------------------------\n`;
			receipt += `Subtotal:        ${formatAmount(cartSumWithoutDiscount)}\n`;
			receipt += `Discount:       -${formatAmount(totalSavings)}\n`;
			receipt += `VAT:             ${formatAmount(vatAmount)}\n`;
			receipt += `------------------------------------------------\n`;
			receipt += `Total Payment:   ${formatAmount(totalSumOfAmountPaid)}\n\n`;

			receipt += `${ESC}a1`;
			receipt += `Thanks for shopping with us!\n`;
			receipt += `Please come again.\n`;
			// receipt += `Opening Hours: 8:15am - 8:30pm\n`;
			// receipt += `VAT No: 01194472-0001\r\n`;
			receipt += `\r\n`;
			receipt += `\r\n`;
			receipt += `\r\n`;
			receipt += `\r\n`;
			receipt += `\r\n`;

			receipt += `${GS}V1`;

			const dataToPrint = toBytes(receipt);
			await this.thermalPrinterUsbDevice?.transferOut(1, dataToPrint);
			await this.thermalPrinterUsbDevice?.close();
		} catch (error) {
			console.error('Error printing receipt:', error);
		}
	};
}

export default ThermalUsbPrinter;
