name: Shoppa Prod CI

env:
  VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
  VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}

on:
  push:
     branches: ['main']

  pull_request:
     branches: ['main']

jobs:
  build:
    name: Build
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node_version: [18]
    steps:
      - uses: actions/checkout@v2
      - name: Set up node ${{matrix.node_version}}
        uses: actions/setup-node@v3
        with:
          node-version: ${{matrix.node_version}}

      - name: Install dependencies
        run: npm ci --force

      - name: Run lint
        run: npm run lint

      - name: Run test
        run: npm run test

      - name: Run Build
        run: CI=true npm run build

  deploy_production:
    name: Deploy to production
    runs-on: ubuntu-latest
    needs: [build]
    steps:
      - uses: actions/checkout@v2
      - name: Install Vercel CLI
        run: npm install --global vercel@canary
      - name: Pull Vercel Environment Information
        run: vercel pull --yes --environment=production --token=${{ secrets.VERCEL_TOKEN }}
      - name: Build Project Artifacts
        run: vercel build --prod --token=${{ secrets.VERCEL_TOKEN }}
      - name: Deploy Project Artifacts to Vercel
        run: vercel deploy --prebuilt --prod --token=${{ secrets.VERCEL_TOKEN }}
