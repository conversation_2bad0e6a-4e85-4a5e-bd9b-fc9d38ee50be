name: Shoppa Staging CI

env:
  VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
  VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}

on:
  push:
   branches: ['staging']

  pull_request:
    branches: ['staging']

jobs:
  build:
    name: Build
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node_version: [18]
    steps:
      - uses: actions/checkout@v2
      - name: Set up node ${{matrix.node_version}}
        uses: actions/setup-node@v3
        with:
          node-version: ${{matrix.node_version}}

      - name: Install dependencies
        run: npm ci --force

      - name: Run lint
        run: npm run lint

      - name: Run test
        run: npm run test

      - name: Run Build
        run: CI=true npm run build



  