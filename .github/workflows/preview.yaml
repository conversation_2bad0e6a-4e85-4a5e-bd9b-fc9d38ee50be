name: Shoppa Preview CI

env:
  VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
  VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}

on:
  push:
    branches:
      - preview
  pull_request:
    branches:
      - preview

jobs:
  Build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node_version: [16]
    steps:
      - uses: actions/checkout@v2
      - name: Set up node ${{matrix.node_version}}
        uses: actions/setup-node@v3
        with:
          node-version: ${{matrix.node_version}}

      - name: Install dependencies
        run: npm ci --force

      - name: Run lint
        run: npm run lint

      - name: Run test
        run: npm run test

      - name: Run Build
        run: CI=true npm run build

  Deploy-Preview:
    runs-on: ubuntu-latest
    needs: Build
    steps:
      - uses: actions/checkout@v2
      - name: Install Vercel CLI
        run: npm install --global vercel@canary
      - name: Pull Vercel Environment Information
        run: vercel pull --yes --environment=preview --token=${{ secrets.VERCEL_TOKEN }}
      - name: Deploy Project Artifacts to Vercel
        run: vercel deploy --prebuilt --token=${{ secrets.VERCEL_TOKEN }}