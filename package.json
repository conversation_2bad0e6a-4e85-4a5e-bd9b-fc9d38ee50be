{"name": "shoppa-app", "version": "0.1.0", "private": true, "dependencies": {"@gaadmaiyaki/field-checker": "^1.2.0", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-slot": "^1.2.3", "@reduxjs/toolkit": "^2.2.5", "@sentry/cli": "^2.36.4", "@sentry/react": "^8.32.0", "@shadcn/ui": "^0.0.4", "@tanstack/react-query": "^5.81.5", "@tanstack/react-table": "^8.21.3", "@testing-library/dom": "^10.4.0", "@types/w3c-web-usb": "^1.0.10", "axios": "^1.7.2", "bcryptjs": "^2.4.3", "bootstrap": "^5.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "crypto-js": "^4.1.1", "date-fns": "^4.1.0", "dayjs": "^1.10.7", "formik": "^2.4.6", "framer-motion": "^11.2.10", "js-big-decimal": "^2.2.0", "lint-staged": "^15.2.9", "localstorage-slim": "^2.2.0", "lodash.throttle": "^4.1.1", "lucide-react": "^0.522.0", "nanoid": "^5.0.9", "pouchdb": "^8.0.1", "pouchdb-authentication": "^1.1.3", "pouchdb-debug": "^7.2.1", "pouchdb-find": "^8.0.1", "pouchdb-upsert": "^2.2.0", "qz-tray": "^2.2.4", "react": "^18.3.1", "react-day-picker": "^9.7.0", "react-dom": "^18.3.1", "react-error-boundary": "^4.0.13", "react-hook-form": "^7.60.0", "react-icons": "^5.5.0", "react-idle-timer": "^5.7.2", "react-lazyload": "^3.2.0", "react-query": "^3.39.3", "react-redux": "^9.1.2", "react-router-dom": "6", "react-scripts": "5.0.1", "react-thermal-printer": "^0.18.1", "react-toastify": "^8.1.1", "react-tooltip": "^4.2.21", "react-window": "^1.8.10", "sass": "^1.77.4", "tailwind-merge": "^3.3.1", "tw-animate-css": "^1.3.4", "typescript": "^4.5.4", "uuid": "^11.1.0", "web-vitals": "^2.1.2", "yup": "^1.4.0", "zod": "^4.0.5"}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx}": ["eslint --fix"], "src/**/*.{js,jsx,ts,tsx,json,css,scss,md}": ["prettier --write"]}, "devDependencies": {"@tailwindcss/postcss": "^4.1.11", "@testing-library/jest-dom": "^6.4.6", "@testing-library/react": "^16.0.0", "@testing-library/react-hooks": "^8.0.1", "@testing-library/user-event": "^13.5.0", "@types/bcryptjs": "^2.4.6", "@types/crypto-js": "^4.1.1", "@types/jest": "^29.5.12", "@types/lodash.throttle": "^4.1.9", "@types/node": "^16.11.19", "@types/pouchdb": "^6.4.2", "@types/pouchdb-upsert": "^2.2.6", "@types/qz-tray": "^2.2.1", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/react-redux": "^7.1.33", "@types/react-window": "^1.8.8", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.21", "husky": "^9.1.4", "postcss": "^8.5.6", "postcss-normalize": "^10.0.1", "prettier": "^3.3.3", "tailwindcss": "3.4.17"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test --watchAll=false", "test:all": "react-scripts test --watch<PERSON>ll", "lint": "eslint 'src/**/*.{tsx,ts}'", "lint:fix": "eslint 'src/**/*.{tsx,ts}' --fix", "eject": "react-scripts eject", "prepare": "husky"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "compilerOptions": {"jsx": "react-jsx"}, "browser": {"crypto": false}}