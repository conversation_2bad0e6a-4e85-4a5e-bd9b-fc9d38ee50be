<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8" />
  <link rel="icon" href="%PUBLIC_URL%/favicon.svg" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <meta name="theme-color" content="#d3a8e9" />
  <meta name="Shoppa POS" content="Shoppa POS application" />
  <link rel="apple-touch-icon" href="%PUBLIC_URL%/favicon.svg" />

  <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />

  <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
  <title>Shoppa POS</title>
</head>

<body style="position: relative">
  <noscript>You need to enable JavaScript to run this app.</noscript>
  <div id="root" className="position-relative"></div>
  <div id="pop-up" className="position-relative"></div>
</body>

</html>